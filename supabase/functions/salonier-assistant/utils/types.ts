
import { ProcessType } from './colorimetry-rules';

// Re-export ProcessType for convenience
export { ProcessType };

export interface FormulaIngredient {
  product: string;
  shade?: string;
  amount: number;
  unit: 'ml' | 'g' | 'cm' | 'oz';
  type: 'color' | 'developer' | 'additive' | 'bleach' | 'toner';
  volume?: number; // For developers
}

export interface FormulaStep {
  id: string;
  title: string;
  ingredients: FormulaIngredient[];
  processingTime: number; // minutes
  instructions: string;
  type: ProcessType;
}

export interface Formula {
  steps: FormulaStep[];
  currentLevel: number;
  desiredLevel: number;
  currentState: 'natural' | 'colored' | 'bleached' | 'mixed';
  brand: string;
  line: string;
  totalTime: number;
  warnings?: string[];
  grayPercentage?: number;
  availableProducts?: ProductAvailability[];
}

export interface ProductAvailability {
  id: string;
  brand: string;
  line: string;
  type: 'color' | 'developer' | 'bleach' | 'toner' | 'additive' | 'pre-pigment';
  shade?: string;
  volume?: number;
  maxShadeLevel?: number;
  maxDeveloperVolume?: number;
  hasDecolorante?: boolean;
  hasToners?: boolean;
  hasPrePigmentation?: boolean;
  availableShades?: string[];
  availableDeveloperVolumes?: number[];
  stock?: number;
  isActive?: boolean;
}

export interface MixingComponent {
  shade: string;
  percentage: number;
  compatibility: 'perfect' | 'good' | 'acceptable' | 'poor';
  risk?: string;
}

export interface MixingSolution {
  targetShade: string;
  components: MixingComponent[];
  expectedResult: string;
  accuracy: 'exact' | 'very-close' | 'approximate' | 'rough';
  warnings: string[];
  instructions: string;
  alternativeProcess?: string;
  brand: string;
  maxComponents: number;
}

export interface ReflectCompatibility {
  reflect1: string;
  reflect2: string;
  compatible: boolean;
  resultReflect?: string;
  warning?: string;
}

// Reglas de mezcla por marca
export interface BrandMixingRules {
  maxComponents: number;
  maxSpecialMixPercentage: number;
  allowedReflectCombinations: ReflectCompatibility[];
  specialMixProducts: string[];
  restrictions: string[];
}

export interface ValidationResult {
  isValid: boolean;
  violations: ValidationViolation[];
  correctedFormula?: Formula;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  confidence: number; // 0-100
}

export interface ValidationViolation {
  type: 'colorimetry' | 'brand' | 'safety' | 'optimization' | 'availability' | 'mixing';
  severity: 'warning' | 'error' | 'critical';
  message: string;
  step?: string;
  ingredient?: string;
  suggestion?: string;
  autoFixAvailable: boolean;
  alternativeProducts?: string[];
  alternativeLines?: string[];
  alternativeBrands?: string[];
  mixingSolution?: MixingSolution;
}
