import { Formula, ProcessType } from './types';

export function calculateExpectedVolume(formula: Formula, stepType: ProcessType): number {
  const levelDifference = formula.desiredLevel - formula.currentLevel;

  switch (stepType) {
    case ProcessType.DIRECT_COLOR:
      if (levelDifference <= 1) return 20;
      if (levelDifference <= 2) return 30;
      return 40;
    case ProcessType.BLEACHING:
      return 30;
    case ProcessType.TONING:
    case ProcessType.PRE_PIGMENTATION:
      return 10;
    default:
      return 20;
  }
}