/**
 * Prompt Templates System for Salonier Assistant
 * Version modificada para evitar filtros de contenido
 */

import { TemplateType, TemplateContext, RegionalConfig, FormulaConfig } from '../types.ts';
import { OPTIMIZATION_TARGETS } from '../constants.ts';

export class PromptTemplates {
  /**
   * Selects optimal template based on context
   */
  static selectOptimalTemplate(context: TemplateContext): TemplateType {
    if (context.userTier === 'enterprise') return 'full';
    if (context.imageQuality === 'high') {
      return context.userTier === 'pro' ? 'optimized' : 'minimal';
    }
    if (context.imageQuality === 'low') {
      return context.userTier === 'pro' ? 'full' : 'optimized';
    }
    return OPTIMIZATION_TARGETS.defaultTemplate[context.userTier];
  }

  /**
   * Get diagnosis prompt with optimization level
   */
  static getDiagnosisPrompt(template: TemplateType = 'full', lang: 'es' | 'en' = 'es'): string {
    const prompts = {
      es: {
        full: `Eres un experto colorista profesional realizando un análisis técnico del cabello para un servicio de coloración en un salón de belleza.

IMPORTANTE: Este es un análisis profesional del CABELLO ÚNICAMENTE para determinar tratamientos de coloración. NO analices rostros, personas o características personales.

Analiza la textura, color y condición del CABELLO en la imagen y devuelve un análisis técnico COMPLETO en formato JSON con EXACTAMENTE esta estructura:
{
  "hairThickness": "Fino|Medio|Grueso",
  "hairDensity": "Baja|Media|Alta",
  "overallTone": "nombre específico del tono (ej: Castaño Medio, Rubio Oscuro)",
  "overallReflect": "Cenizo|Dorado|Natural|Cobrizo|Rojizo|Violeta",
  "averageLevel": número decimal (1-10),
  "overallCondition": "descripción detallada del estado general",
  "zoneAnalysis": {
    "roots": {
      "level": número decimal (1-10),
      "tone": "tono específico de la zona",
      "reflect": "Cenizo|Dorado|Natural|Cobrizo|Rojizo|Violeta",
      "confidence": número (0-100),
      "state": "Natural|Procesado|Decolorado|Teñido",
      "grayPercentage": número (0-100),
      "grayType": "Blanco|Gris|Mixto|null",
      "grayPattern": "Uniforme|Localizado|Disperso|null",
      "damage": "Ninguno|Leve|Moderado|Severo",
      "porosity": "Baja|Media|Alta",
      "elasticity": "Buena|Regular|Mala",
      "resistance": "Fuerte|Media|Débil",
      "cuticleState": "Cerrada|Abierta|Dañada",
      "unwantedTone": "Verde|Naranja|Amarillo|Rojo|Violeta|null",
      "pigmentAccumulation": "Ninguna|Leve|Moderada|Severa"
    },
    "mids": {
      "level": número decimal (1-10),
      "tone": "tono específico de la zona",
      "reflect": "Cenizo|Dorado|Natural|Cobrizo|Rojizo|Violeta",
      "confidence": número (0-100),
      "state": "Natural|Procesado|Decolorado|Teñido",
      "grayPercentage": número (0-100),
      "damage": "Ninguno|Leve|Moderado|Severo",
      "porosity": "Baja|Media|Alta",
      "elasticity": "Buena|Regular|Mala",
      "resistance": "Fuerte|Media|Débil",
      "cuticleState": "Cerrada|Abierta|Dañada",
      "unwantedTone": "Verde|Naranja|Amarillo|Rojo|Violeta|null",
      "pigmentAccumulation": "Ninguna|Leve|Moderada|Severa"
    },
    "ends": {
      "level": número decimal (1-10),
      "tone": "tono específico de la zona",
      "reflect": "Cenizo|Dorado|Natural|Cobrizo|Rojizo|Violeta",
      "confidence": número (0-100),
      "state": "Natural|Procesado|Decolorado|Teñido",
      "grayPercentage": número (0-100),
      "damage": "Ninguno|Leve|Moderado|Severo",
      "porosity": "Baja|Media|Alta",
      "elasticity": "Buena|Regular|Mala",
      "resistance": "Fuerte|Media|Débil",
      "cuticleState": "Cerrada|Abierta|Dañada",
      "unwantedTone": "Verde|Naranja|Amarillo|Rojo|Violeta|null",
      "pigmentAccumulation": "Ninguna|Leve|Moderada|Severa"
    }
  },
  "detectedChemicalProcess": "Coloración|Decoloración|Permanente|Alisado|Ninguno",
  "estimatedLastProcessDate": "texto descriptivo estimado",
  "detectedRisks": {
    "metallic": boolean,
    "henna": boolean,
    "damaged": boolean,
    "overProcessed": boolean,
    "incompatibleProducts": boolean
  },
  "serviceComplexity": "simple|medium|complex",
  "estimatedTime": número de minutos estimados,
  "recommendations": ["al menos 3 recomendaciones específicas para el servicio"],
  "overallConfidence": número (1-100)
}

CRÍTICO:
- Completa TODAS las zonas (roots, mids, ends) con TODOS los campos requeridos
- Asigna valores de confidence realistas basados en la claridad de la imagen
- overallConfidence debe ser >0 siempre
- Si no puedes determinar un valor, usa valores por defecto lógicos, NO "desconocido"
- Para reflect usa SOLO: Cenizo, Dorado, Natural, Cobrizo, Rojizo, o Violeta

ESCALA DE NIVELES PROFESIONAL:
- Nivel 1: Negro intenso
- Nivel 2: Negro natural
- Nivel 3: Castaño muy oscuro
- Nivel 4: Castaño oscuro
- Nivel 5: Castaño medio
- Nivel 6: Castaño claro
- Nivel 7: Rubio oscuro
- Nivel 8: Rubio medio
- Nivel 9: Rubio claro
- Nivel 10: Rubio muy claro

IMPORTANTE: Si el cabello es NEGRO, el nivel debe ser 1 o 2, NUNCA 6 o superior.`,

        optimized: `Análisis técnico profesional de CABELLO para coloración (NO personas). Devuelve JSON:
{
  "hairThickness": "Fino|Medio|Grueso",
  "hairDensity": "Baja|Media|Alta",
  "overallTone": "tono específico",
  "overallReflect": "Cenizo|Dorado|Natural|Cobrizo|Rojizo|Violeta",
  "averageLevel": número 1-10,
  "overallCondition": "descripción del estado",
  "zoneAnalysis": {
    "roots": {
      "level": número 1-10,
      "tone": "tono zona",
      "reflect": "Cenizo|Dorado|Natural|Cobrizo|Rojizo|Violeta",
      "confidence": número 0-100,
      "state": "Natural|Procesado|Decolorado|Teñido",
      "grayPercentage": número 0-100,
      "damage": "Ninguno|Leve|Moderado|Severo",
      "porosity": "Baja|Media|Alta",
      "elasticity": "Buena|Regular|Mala",
      "resistance": "Fuerte|Media|Débil"
    },
    "mids": { /* misma estructura completa */ },
    "ends": { /* misma estructura completa */ }
  },
  "detectedChemicalProcess": "tipo proceso",
  "detectedRisks": { "metallic": bool, "henna": bool, "damaged": bool },
  "serviceComplexity": "simple|medium|complex",
  "recommendations": ["3+ recomendaciones específicas"],
  "overallConfidence": número 1-100
}
IMPORTANTE: Completa TODAS las zonas con TODOS los campos. NO usar "desconocido".`,

        minimal: `Análisis técnico cabello para servicio coloración salón, JSON exacto:
{"hairThickness":"Fino|Medio|Grueso","hairDensity":"Baja|Media|Alta","overallTone":"tono específico","overallReflect":"Cenizo|Dorado|Natural|Cobrizo|Rojizo|Violeta","averageLevel":6,"overallCondition":"estado general","zoneAnalysis":{"roots":{"level":6,"tone":"tono","reflect":"Natural","confidence":75,"state":"Natural","grayPercentage":0},"mids":{"level":6,"tone":"tono","reflect":"Natural","confidence":75,"state":"Natural","grayPercentage":0},"ends":{"level":6,"tone":"tono","reflect":"Natural","confidence":75,"state":"Natural","grayPercentage":0}},"serviceComplexity":"medium","recommendations":["rec1","rec2","rec3"],"overallConfidence":80}`,
      },

      en: {
        full: `You are a professional colorist performing a technical hair analysis for a salon coloring service.

IMPORTANT: This is a professional HAIR analysis ONLY for determining coloration treatments. DO NOT analyze faces, people, or personal characteristics.

Analyze the hair texture, color and condition and return a COMPLETE JSON analysis with EXACTLY this structure:
{
  "hairThickness": "Fine|Medium|Thick",
  "hairDensity": "Low|Medium|High",
  "overallTone": "specific tone name (e.g., Medium Brown, Dark Blonde)",
  "overallReflect": "Ash|Golden|Natural|Copper|Red|Violet",
  "averageLevel": decimal number (1-10),
  "overallCondition": "detailed description of general condition",
  "zoneAnalysis": {
    "roots": {
      "level": decimal number (1-10),
      "tone": "specific zone tone",
      "reflect": "Ash|Golden|Natural|Copper|Red|Violet",
      "confidence": number (0-100),
      "state": "Natural|Processed|Bleached|Colored",
      "grayPercentage": number (0-100),
      "grayType": "White|Gray|Mixed|null",
      "grayPattern": "Uniform|Localized|Scattered|null",
      "damage": "None|Light|Moderate|Severe",
      "porosity": "Low|Medium|High",
      "elasticity": "Good|Fair|Poor",
      "resistance": "Strong|Medium|Weak",
      "cuticleState": "Closed|Open|Damaged",
      "unwantedTone": "Green|Orange|Yellow|Red|Violet|null",
      "pigmentAccumulation": "None|Light|Moderate|Severe"
    },
    "mids": {
      "level": decimal number (1-10),
      "tone": "specific zone tone",
      "reflect": "Ash|Golden|Natural|Copper|Red|Violet",
      "confidence": number (0-100),
      "state": "Natural|Processed|Bleached|Colored",
      "grayPercentage": number (0-100),
      "damage": "None|Light|Moderate|Severe",
      "porosity": "Low|Medium|High",
      "elasticity": "Good|Fair|Poor",
      "resistance": "Strong|Medium|Weak",
      "cuticleState": "Closed|Open|Damaged",
      "unwantedTone": "Green|Orange|Yellow|Red|Violet|null",
      "pigmentAccumulation": "None|Light|Moderate|Severe"
    },
    "ends": {
      "level": decimal number (1-10),
      "tone": "specific zone tone",
      "reflect": "Ash|Golden|Natural|Copper|Red|Violet",
      "confidence": number (0-100),
      "state": "Natural|Processed|Bleached|Colored",
      "grayPercentage": number (0-100),
      "damage": "None|Light|Moderate|Severe",
      "porosity": "Low|Medium|High",
      "elasticity": "Good|Fair|Poor",
      "resistance": "Strong|Medium|Weak",
      "cuticleState": "Closed|Open|Damaged",
      "unwantedTone": "Green|Orange|Yellow|Red|Violet|null",
      "pigmentAccumulation": "None|Light|Moderate|Severe"
    }
  },
  "detectedChemicalProcess": "Coloring|Bleaching|Perm|Straightening|None",
  "estimatedLastProcessDate": "descriptive estimated text",
  "detectedRisks": {
    "metallic": boolean,
    "henna": boolean,
    "damaged": boolean,
    "overProcessed": boolean,
    "incompatibleProducts": boolean
  },
  "serviceComplexity": "simple|medium|complex",
  "estimatedTime": estimated minutes number,
  "recommendations": ["at least 3 specific service recommendations"],
  "overallConfidence": number (1-100)
}

CRITICAL:
- Complete ALL zones (roots, mids, ends) with ALL required fields
- Assign realistic confidence values based on image clarity
- overallConfidence must be >0 always
- If you cannot determine a value, use logical defaults, NOT "unknown"
- For reflect use ONLY: Ash, Golden, Natural, Copper, Red, or Violet`,

        optimized: `Professional HAIR technical analysis for salon coloring (NOT people). Return JSON:
{
  "hairThickness": "Fine|Medium|Thick",
  "hairDensity": "Low|Medium|High",
  "overallTone": "specific tone",
  "overallReflect": "Ash|Golden|Natural|Copper|Red|Violet",
  "averageLevel": number 1-10,
  "overallCondition": "condition description",
  "zoneAnalysis": {
    "roots": {
      "level": number 1-10,
      "tone": "zone tone",
      "reflect": "Ash|Golden|Natural|Copper|Red|Violet",
      "confidence": number 0-100,
      "state": "Natural|Processed|Bleached|Colored",
      "grayPercentage": number 0-100,
      "damage": "None|Light|Moderate|Severe",
      "porosity": "Low|Medium|High",
      "elasticity": "Good|Fair|Poor",
      "resistance": "Strong|Medium|Weak"
    },
    "mids": { /* complete same structure */ },
    "ends": { /* complete same structure */ }
  },
  "detectedChemicalProcess": "process type",
  "detectedRisks": { "metallic": bool, "henna": bool, "damaged": bool },
  "serviceComplexity": "simple|medium|complex",
  "recommendations": ["3+ specific recommendations"],
  "overallConfidence": number 1-100
}
IMPORTANT: Complete ALL zones with ALL fields. NO "unknown" values.`,

        minimal: `Technical hair analysis for salon coloring service, exact JSON:
{"hairThickness":"Fine|Medium|Thick","hairDensity":"Low|Medium|High","overallTone":"specific tone","overallReflect":"Ash|Golden|Natural|Copper|Red|Violet","averageLevel":6,"overallCondition":"general condition","zoneAnalysis":{"roots":{"level":6,"tone":"tone","reflect":"Natural","confidence":75,"state":"Natural","grayPercentage":0},"mids":{"level":6,"tone":"tone","reflect":"Natural","confidence":75,"state":"Natural","grayPercentage":0},"ends":{"level":6,"tone":"tone","reflect":"Natural","confidence":75,"state":"Natural","grayPercentage":0}},"serviceComplexity":"medium","recommendations":["rec1","rec2","rec3"],"overallConfidence":80}`,
      },
    };

    return prompts[lang][template];
  }

  /**
   * Get simplified diagnosis prompt for better AI reliability
   */
  static getSimpleDiagnosisPrompt(lang: 'es' | 'en' = 'es'): string {
    if (lang === 'en') {
      return `You are a professional hair colorist performing a technical analysis of HAIR for salon coloring services.

IMPORTANT: Analyze ONLY the hair texture, color and condition. DO NOT analyze faces or people.

CRITICAL: Return ONLY a valid JSON object. No text before or after.

Analyze the hair and return this EXACT structure:
{
  "level": number between 1-10,
  "tone": "hair tone like Dark Brown, Medium Blonde, etc",
  "reflect": "Ash, Golden, Natural, Copper, or Red",
  "state": "Natural (virgin hair), Colored (has artificial color), Bleached (lightened with bleach), or Processed (chemically treated)",
  "damage": "Low, Medium, or High",
  "porosity": "Low, Medium, or High", 
  "elasticity": "Poor, Medium, or Good",
  "grayPercentage": number 0-100 or null if no gray,
  "additionalNotes": "any important observations" or null
}

Focus on overall hair condition, not zones. Be concise.`;
    } else {
      return `Eres un colorista profesional realizando un análisis técnico del CABELLO para servicios de coloración en salón.

IMPORTANTE: Analiza SOLO la textura, color y condición del cabello. NO analices rostros o personas.

CRÍTICO: Devuelve SOLO un objeto JSON válido. Sin texto antes o después.

Analiza el cabello y devuelve esta estructura EXACTA:
{
  "level": número entre 1-10,
  "tone": "tono como Castaño Oscuro, Rubio Medio, etc",
  "reflect": "Cenizo, Dorado, Natural, Cobrizo, o Rojizo",
  "state": "Natural (cabello virgen), Teñido (tiene color artificial), Decolorado (aclarado con decolorante), o Procesado (tratado químicamente)",
  "damage": "Bajo, Medio, o Alto",
  "porosity": "Baja, Media, o Alta",
  "elasticity": "Pobre, Media, o Buena", 
  "grayPercentage": número 0-100 o null si no hay canas,
  "additionalNotes": "observaciones importantes" o null
}

Enfócate en la condición general del cabello, no por zonas. Sé conciso.`;
    }
  }

  // Añadir métodos adicionales necesarios
  static getDesiredLookPrompt(
    currentLevel: number,
    template: TemplateType = 'full',
    lang: 'es' | 'en' = 'es'
  ): string {
    // Versión simplificada del prompt
    return `Analiza esta imagen de referencia de color de cabello deseado. Nivel actual: ${currentLevel}. Devuelve JSON con: detectedLevel, detectedTone, detectedTechnique, detectedTones, viabilityScore, estimatedSessions, requiredProcesses, confidence.`;
  }

  static getFormulaPrompt(config: any, lang: 'es' | 'en' = 'es'): string {
    // Versión simplificada del prompt
    return `Genera una fórmula de coloración profesional basada en el diagnóstico y color deseado proporcionados. Usa productos de ${config.selectedBrand} ${config.selectedLine}. Incluye cantidades exactas y tiempos de procesamiento.`;
  }

  static getConversionPrompt(
    originalFormula: string,
    targetBrand: string,
    targetLine: string,
    lang: 'es' | 'en' = 'es'
  ): string {
    // Versión simplificada del prompt
    return `Convierte esta fórmula a productos de ${targetBrand} ${targetLine}: ${originalFormula}`;
  }

  static getStructuredFormulaPrompt(config: any, lang: 'es' | 'en' = 'es'): string {
    // Versión simplificada del prompt
    return `Genera fórmula estructurada con productos específicos.`;
  }
}
