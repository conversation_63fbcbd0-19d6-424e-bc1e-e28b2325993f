/**
 * Mixing Validator
 * Intelligent mixing system that suggests blends when exact shades aren't available
 * Handles complex 2-3 component mixing with brand-specific rules and chemical compatibility
 */

import {
  FormulaIngredient,
  Formula,
  ProductAvailability,
  MixingComponent,
  MixingSolution,
  ReflectCompatibility,
  BrandMixingRules,
  ValidationViolation,
} from '../types';

/**
 * Main mixing validation function
 */
export function validateAndSuggestMixing(
  formula: Formula,
  brandRules: Record<string, any>
): ValidationViolation[] {
  const violations: ValidationViolation[] = [];
  const availableProducts = formula.availableProducts || [];
  const brandMixingRules = getBrandMixingRules(formula.brand);

  // Check each color ingredient for mixing opportunities
  for (const step of formula.steps) {
    for (const ingredient of step.ingredients) {
      if (ingredient.type === 'color' && ingredient.shade) {
        const isAvailable = isShadeDirectlyAvailable(
          ingredient.shade,
          availableProducts,
          formula.brand,
          formula.line
        );

        if (!isAvailable) {
          // Generate intelligent mixing solution
          const mixingSolution = generateMixingSolution(
            ingredient.shade,
            availableProducts,
            formula.brand,
            formula.line,
            brandMixingRules
          );

          if (mixingSolution) {
            violations.push({
              type: 'mixing',
              severity: 'warning',
              message: `Tono ${ingredient.shade} no disponible - se propone mezcla inteligente`,
              step: step.id,
              ingredient: ingredient.product,
              suggestion: `${mixingSolution.expectedResult}: ${mixingSolution.components.map(c => `${c.percentage}% ${c.shade}`).join(' + ')}`,
              autoFixAvailable: true,
              mixingSolution,
            });
          } else {
            // If no mixing solution possible, suggest alternatives
            const alternatives = suggestCreativeAlternatives(
              ingredient.shade,
              availableProducts,
              formula.brand,
              formula.line
            );

            violations.push({
              type: 'mixing',
              severity: 'error',
              message: `Tono ${ingredient.shade} no disponible y no se puede conseguir por mezcla`,
              step: step.id,
              ingredient: ingredient.product,
              suggestion: alternatives.length > 0 ? `Alternativas: ${alternatives.join(', ')}` : 'Considerar cambiar la fórmula o conseguir el tono específico',
              autoFixAvailable: alternatives.length > 0,
              alternativeProducts: alternatives,
            });
          }
        }
      }
    }
  }

  return violations;
}

/**
 * Generate intelligent mixing solution for unavailable shade
 */
export function generateMixingSolution(
  targetShade: string,
  availableProducts: ProductAvailability[],
  brand: string,
  line: string,
  mixingRules: BrandMixingRules
): MixingSolution | null {
  const parsedTarget = parseShade(targetShade);
  if (!parsedTarget) return null;

  // Get available shades in same brand/line
  const availableShades = getAvailableShades(availableProducts, brand, line);

  // Find potential mixing combinations
  const combinations = findMixingCombinations(
    parsedTarget,
    availableShades,
    mixingRules
  );

  // Select best combination
  const bestCombination = selectBestMixingCombination(combinations, parsedTarget);

  if (bestCombination) {
    return {
      targetShade,
      components: bestCombination.components,
      expectedResult: bestCombination.expectedResult,
      accuracy: bestCombination.accuracy,
      warnings: bestCombination.warnings,
      instructions: generateMixingInstructions(bestCombination, brand),
      alternativeProcess: bestCombination.alternativeProcess,
      brand,
      maxComponents: mixingRules.maxComponents,
    };
  }

  return null;
}

/**
 * Parse shade into level and reflects
 */
export function parseShade(shade: string): { level: number; primaryReflect?: string; secondaryReflect?: string } | null {
  // Examples: "7.43", "8/34", "9.03", "6N", "7.4"
  const patterns = [
    /^(\d+)\.(\d)(\d)?$/, // 7.43, 8.3
    /^(\d+)\/(\d)(\d)?$/, // 7/43, 8/3
    /^(\d+)([A-Z])$/, // 6N, 7A
    /^(\d+)$/, // Just level: 7
  ];

  for (const pattern of patterns) {
    const match = shade.match(pattern);
    if (match) {
      const level = parseInt(match[1]);

      if (match[2]) {
        if (isNaN(parseInt(match[2]))) {
          // Letter notation (N, A, etc.)
          return {
            level,
            primaryReflect: match[2],
          };
        } else {
          // Numeric notation
          return {
            level,
            primaryReflect: match[2],
            secondaryReflect: match[3],
          };
        }
      }

      return { level };
    }
  }

  return null;
}

/**
 * Get available shades from products
 */
export function getAvailableShades(
  availableProducts: ProductAvailability[],
  brand: string,
  line: string
): string[] {
  return availableProducts
    .filter(p =>
      p.brand.toLowerCase() === brand.toLowerCase() &&
      p.line.toLowerCase() === line.toLowerCase() &&
      p.type === 'color' &&
      p.isActive !== false
    )
    .flatMap(p => p.availableShades || [])
    .filter((shade, index, array) => array.indexOf(shade) === index); // Remove duplicates
}

/**
 * Find all possible mixing combinations
 */
export function findMixingCombinations(
  target: { level: number; primaryReflect?: string; secondaryReflect?: string },
  availableShades: string[],
  mixingRules: BrandMixingRules
): Array<{
  components: MixingComponent[];
  expectedResult: string;
  accuracy: 'exact' | 'very-close' | 'approximate' | 'rough';
  warnings: string[];
  alternativeProcess?: string;
}> {
  const combinations: any[] = [];

  // Try two-component mixing first (most common)
  for (let i = 0; i < availableShades.length; i++) {
    for (let j = i + 1; j < availableShades.length; j++) {
      const shade1 = parseShade(availableShades[i]);
      const shade2 = parseShade(availableShades[j]);

      if (!shade1 || !shade2) continue;

      // Check level compatibility (should be same or very close)
      if (Math.abs(shade1.level - target.level) <= 1 &&
          Math.abs(shade2.level - target.level) <= 1) {

        const mixingResult = calculateTwoShadeMix(
          { shade: availableShades[i], parsed: shade1 },
          { shade: availableShades[j], parsed: shade2 },
          target,
          mixingRules
        );

        if (mixingResult) {
          combinations.push(mixingResult);
        }
      }
    }
  }

  // Try three-component mixing if brand allows it
  if (mixingRules.maxComponents >= 3) {
    // Implementation for 3-component mixing (more complex)
    // This would be added for brands that allow more complex mixtures
  }

  return combinations;
}

/**
 * Calculate result of mixing two shades
 */
export function calculateTwoShadeMix(
  shade1: { shade: string; parsed: any },
  shade2: { shade: string; parsed: any },
  target: any,
  mixingRules: BrandMixingRules
): any | null {
  // Check reflect compatibility
  const reflectCompatibility = checkReflectCompatibility(
    shade1.parsed.primaryReflect,
    shade2.parsed.primaryReflect,
    mixingRules
  );

  if (!reflectCompatibility.compatible) {
    return null; // Incompatible reflects
  }

  // Calculate optimal percentages
  const percentages = calculateOptimalPercentages(
    shade1.parsed,
    shade2.parsed,
    target
  );

  if (!percentages) return null;

  const components: MixingComponent[] = [
    {
      shade: shade1.shade,
      percentage: percentages.shade1Percentage,
      compatibility: reflectCompatibility.compatible ? 'good' : 'poor',
    },
    {
      shade: shade2.shade,
      percentage: percentages.shade2Percentage,
      compatibility: reflectCompatibility.compatible ? 'good' : 'poor',
    },
  ];

  // Determine accuracy and warnings
  const accuracy = determineAccuracy(shade1.parsed, shade2.parsed, target, percentages);
  const warnings = generateMixingWarnings(components, target, reflectCompatibility);

  return {
    components,
    expectedResult: `Aproximado a ${target.level}.${target.primaryReflect || ''}${target.secondaryReflect || ''}`,
    accuracy,
    warnings,
  };
}

/**
 * Check if two reflects are compatible for mixing
 */
export function checkReflectCompatibility(
  reflect1?: string,
  reflect2?: string,
  mixingRules?: BrandMixingRules
): ReflectCompatibility {
  if (!reflect1 || !reflect2) {
    return { reflect1: reflect1 || '', reflect2: reflect2 || '', compatible: true };
  }

  // Define incompatible combinations
  const incompatibleCombinations = [
    { r1: '1', r2: '3', warning: 'Ceniza + dorado = resultado impredecible' },
    { r1: '1', r2: '4', warning: 'Ceniza + cobrizo = neutralización excesiva' },
    { r1: '2', r2: '6', warning: 'Violeta + rojo = resultado muy oscuro' },
    { r1: '1', r2: '7', warning: 'Ceniza + verde = resultado apagado' },
  ];

  // Check brand-specific rules if available
  if (mixingRules?.allowedReflectCombinations) {
    const ruleMatch = mixingRules.allowedReflectCombinations.find(
      rule =>
        (rule.reflect1 === reflect1 && rule.reflect2 === reflect2) ||
        (rule.reflect1 === reflect2 && rule.reflect2 === reflect1)
    );

    if (ruleMatch) {
      return ruleMatch;
    }
  }

  // Check general incompatibilities
  const incompatible = incompatibleCombinations.find(
    combo =>
      (combo.r1 === reflect1 && combo.r2 === reflect2) ||
      (combo.r1 === reflect2 && combo.r2 === reflect1)
  );

  if (incompatible) {
    return {
      reflect1,
      reflect2,
      compatible: false,
      warning: incompatible.warning,
    };
  }

  // Default: compatible
  return {
    reflect1,
    reflect2,
    compatible: true,
    resultReflect: predictResultReflect(reflect1, reflect2),
  };
}

/**
 * Predict resulting reflect from mixing two reflects
 */
export function predictResultReflect(reflect1: string, reflect2: string): string {
  // Simplified prediction logic
  const reflectMap: Record<string, number> = {
    '0': 0, '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    'N': 0, 'A': 1, 'V': 2, 'G': 3, 'C': 4, 'M': 5, 'R': 6,
  };

  const val1 = reflectMap[reflect1] ?? 0;
  const val2 = reflectMap[reflect2] ?? 0;
  const average = Math.round((val1 + val2) / 2);

  return average.toString();
}

/**
 * Calculate optimal mixing percentages
 */
export function calculateOptimalPercentages(
  shade1: any,
  shade2: any,
  target: any
): { shade1Percentage: number; shade2Percentage: number } | null {
  // If same level, can mix 50/50 as starting point
  if (shade1.level === shade2.level && shade1.level === target.level) {
    return { shade1Percentage: 50, shade2Percentage: 50 };
  }

  // If different levels, calculate based on target level
  if (shade1.level !== shade2.level) {
    // Linear interpolation to get target level
    const levelDiff = shade2.level - shade1.level;
    const targetDiff = target.level - shade1.level;

    if (levelDiff === 0) return null; // Same level, shouldn't happen

    const shade2Percentage = Math.round((targetDiff / levelDiff) * 100);
    const shade1Percentage = 100 - shade2Percentage;

    // Ensure reasonable percentages (10-90%)
    if (shade1Percentage < 10 || shade1Percentage > 90) return null;

    return { shade1Percentage, shade2Percentage };
  }

  return { shade1Percentage: 50, shade2Percentage: 50 };
}

/**
 * Determine accuracy of mixing result
 */
export function determineAccuracy(
  shade1: any,
  shade2: any,
  target: any,
  percentages: any
): 'exact' | 'very-close' | 'approximate' | 'rough' {
  // Same level and compatible reflects = very close
  if (shade1.level === target.level && shade2.level === target.level) {
    if (shade1.primaryReflect === target.primaryReflect || shade2.primaryReflect === target.primaryReflect) {
      return 'very-close';
    }
    return 'approximate';
  }

  // Different levels but close = approximate
  const avgLevel = (shade1.level * percentages.shade1Percentage + shade2.level * percentages.shade2Percentage) / 100;
  if (Math.abs(avgLevel - target.level) <= 0.5) {
    return 'approximate';
  }

  return 'rough';
}

/**
 * Generate warnings for mixing
 */
export function generateMixingWarnings(
  components: MixingComponent[],
  target: any,
  reflectCompatibility: ReflectCompatibility
): string[] {
  const warnings: string[] = [];

  if (!reflectCompatibility.compatible && reflectCompatibility.warning) {
    warnings.push(reflectCompatibility.warning);
  }

  if (components.some(c => c.percentage < 20)) {
    warnings.push('Porcentajes muy bajos pueden ser difíciles de medir con precisión');
  }

  if (components.some(c => c.percentage > 80)) {
    warnings.push('Un componente domina la mezcla - el resultado será muy similar al tono principal');
  }

  return warnings;
}

/**
 * Generate mixing instructions
 */
export function generateMixingInstructions(
  combination: any,
  brand: string
): string {
  const instructions = [
    '1. Medir con precisión cada componente',
    `2. Mezclar ${combination.components.map((c: any) => `${c.percentage}% ${c.shade}`).join(' + ')}`,
    '3. Homogeneizar completamente antes de aplicar',
    '4. Realizar prueba mechas si hay dudas sobre el resultado',
  ];

  // Add brand-specific instructions
  const brandInstructions = getBrandSpecificMixingInstructions(brand);
  if (brandInstructions.length > 0) {
    instructions.push('\nInstrucciones específicas de ' + brand + ':');
    instructions.push(...brandInstructions);
  }

  return instructions.join('\n');
}

/**
 * Get brand-specific mixing instructions
 */
export function getBrandSpecificMixingInstructions(brand: string): string[] {
  const brandInstructions: Record<string, string[]> = {
    'wella': [
      '• Special Mix no debe superar el 25% de la mezcla',
      '• Koleston Perfect acepta hasta 3 tonos en mezcla',
      '• Illumina Color máximo 2 tonos para mantener tecnología',
    ],
    'loreal': [
      '• Majirel: máximo 2 tonos base + corrector',
      '• INOA: evitar mezclar con tonos de cobertura',
      '• Mix correctores hasta 1/4 del tono base',
    ],
    'schwarzkopf': [
      '• Igora Royal: concentrados 0/xx máximo 10%',
      '• Absolutes pueden mezclarse libremente entre sí',
      '• Royal Highlifts no mezclar con tonos oscuros',
    ],
  };

  return brandInstructions[brand.toLowerCase()] || [];
}

/**
 * Get brand mixing rules
 */
export function getBrandMixingRules(brand: string): BrandMixingRules {
  const defaultRules: BrandMixingRules = {
    maxComponents: 2,
    maxSpecialMixPercentage: 20,
    allowedReflectCombinations: [],
    specialMixProducts: [],
    restrictions: [],
  };

  const brandRules: Record<string, Partial<BrandMixingRules>> = {
    'wella': {
      maxComponents: 3,
      maxSpecialMixPercentage: 25,
      specialMixProducts: ['Special Mix', 'T-Series'],
      restrictions: ['Illumina Color máximo 2 tonos'],
    },
    'loreal': {
      maxComponents: 2,
      maxSpecialMixPercentage: 25,
      specialMixProducts: ['Mix', '.1', '.2'],
      restrictions: ['INOA no mezclar con cobertura'],
    },
    'schwarzkopf': {
      maxComponents: 2,
      maxSpecialMixPercentage: 10,
      specialMixProducts: ['0/xx concentrados'],
      restrictions: ['Royal Highlifts solo entre sí'],
    },
  };

  return { ...defaultRules, ...brandRules[brand.toLowerCase()] };
}

/**
 * Check if shade is directly available
 */
export function isShadeDirectlyAvailable(
  shade: string,
  availableProducts: ProductAvailability[],
  brand: string,
  line: string
): boolean {
  return availableProducts.some(p =>
    p.brand.toLowerCase() === brand.toLowerCase() &&
    p.line.toLowerCase() === line.toLowerCase() &&
    p.type === 'color' &&
    p.availableShades?.includes(shade) &&
    p.isActive !== false
  );
}

/**
 * Suggest creative alternatives when mixing isn't possible
 */
export function suggestCreativeAlternatives(
  targetShade: string,
  availableProducts: ProductAvailability[],
  brand: string,
  line: string
): string[] {
  const alternatives: string[] = [];
  const parsedTarget = parseShade(targetShade);

  if (!parsedTarget) return alternatives;

  // 1. Suggest using decolorante + toner process
  if (parsedTarget.level >= 7) {
    const hasDecolorante = availableProducts.some(p =>
      p.brand.toLowerCase() === brand.toLowerCase() &&
      p.type === 'bleach' && p.hasDecolorante
    );

    const hasToners = availableProducts.some(p =>
      p.brand.toLowerCase() === brand.toLowerCase() &&
      p.type === 'toner' && p.hasToners
    );

    if (hasDecolorante && hasToners) {
      alternatives.push(`Aclarado + matización para conseguir ${targetShade}`);
    }
  }

  // 2. Suggest using cold tones for neutralization
  if (parsedTarget.primaryReflect && ['1', '2', '6'].includes(parsedTarget.primaryReflect)) {
    const coldTones = getAvailableShades(availableProducts, brand, line)
      .filter(shade => {
        const parsed = parseShade(shade);
        return parsed && parsed.level === parsedTarget.level &&
               parsed.primaryReflect && ['1', '2', '6'].includes(parsed.primaryReflect);
      });

    if (coldTones.length > 0) {
      alternatives.push(`Usar tonos fríos disponibles: ${coldTones.slice(0, 3).join(', ')}`);
    }
  }

  // 3. Suggest level adjustment with similar reflect
  const similarReflectTones = getAvailableShades(availableProducts, brand, line)
    .filter(shade => {
      const parsed = parseShade(shade);
      return parsed &&
             parsed.primaryReflect === parsedTarget.primaryReflect &&
             Math.abs(parsed.level - parsedTarget.level) <= 1;
    });

  if (similarReflectTones.length > 0) {
    alternatives.push(`Nivel similar con mismo reflejo: ${similarReflectTones.slice(0, 2).join(', ')}`);
  }

  // 4. Suggest multi-step process
  if (parsedTarget.level <= 4) {
    alternatives.push('Proceso en 2 pasos: pre-pigmentación + tono final');
  }

  return alternatives;
}

/**
 * Select best mixing combination from available options
 */
export function selectBestMixingCombination(
  combinations: any[],
  target: any
): any | null {
  if (combinations.length === 0) return null;

  // Sort by accuracy and feasibility
  const sorted = combinations.sort((a, b) => {
    const accuracyScore = { 'exact': 4, 'very-close': 3, 'approximate': 2, 'rough': 1 };
    const aScore = accuracyScore[a.accuracy] || 0;
    const bScore = accuracyScore[b.accuracy] || 0;

    if (aScore !== bScore) return bScore - aScore;

    // If same accuracy, prefer fewer warnings
    return a.warnings.length - b.warnings.length;
  });

  return sorted[0];
}

/**
 * Correct mixing issues by applying suggested mixing solution
 */
export function correctMixingIssue(formula: Formula, violation: ValidationViolation): Formula {
  const corrected = { ...formula };

  if (violation.mixingSolution && violation.step) {
    const step = corrected.steps.find(s => s.id === violation.step);
    if (step) {
      const targetIngredient = step.ingredients.find(ing => ing.product === violation.ingredient);
      if (targetIngredient && targetIngredient.shade) {
        // Replace single ingredient with mixing components
        const ingredientIndex = step.ingredients.findIndex(ing => ing === targetIngredient);

        // Create new ingredients based on mixing solution
        const mixingIngredients: FormulaIngredient[] = violation.mixingSolution.components.map((component, index) => ({
          product: `${targetIngredient.product} - Componente ${index + 1}`,
          shade: component.shade,
          amount: Math.round((targetIngredient.amount * component.percentage) / 100),
          unit: targetIngredient.unit,
          type: targetIngredient.type,
        }));

        // Replace original ingredient with mixing components
        step.ingredients.splice(ingredientIndex, 1, ...mixingIngredients);

        // Update step title and instructions
        step.title += ' (Mezcla inteligente)';
        step.instructions += `\n\nMezcla: ${violation.mixingSolution.instructions}`;

        // Add warnings to formula
        corrected.warnings = [
          ...(corrected.warnings || []),
          `Tono ${targetIngredient.shade} reemplazado por mezcla inteligente`,
          ...violation.mixingSolution.warnings,
        ];
      }
    }
  }

  return corrected;
}