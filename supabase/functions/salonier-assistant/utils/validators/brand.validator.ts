
import { Formula, ValidationViolation } from '../types';

function validateSpecialProducts(formula: Formula, violations: ValidationViolation[]): void {
  // Brand-specific special product validation logic
  // This would be expanded based on specific brand rules
}

function validateDeveloperCompatibility(
  formula: Formula,
  violations: ValidationViolation[],
  brandRules: Record<string, any>
): void {
  // Check if developer volumes are allowed for the brand
  for (const step of formula.steps) {
    const developer = step.ingredients.find(ing => ing.type === 'developer');
    if (developer && developer.volume) {
      if (!brandRules.allowedDeveloperVolumes.includes(developer.volume)) {
        violations.push({
          type: 'brand',
          severity: 'warning',
          message: `Volumen ${developer.volume} no estándar para ${formula.brand}`,
          step: step.id,
          suggestion: `Usar volúmenes estándar: ${brandRules.allowedDeveloperVolumes.join(', ')}`,
          autoFixAvailable: true,
        });
      }
    }
  }
}

export function validateBrandRules(
  formula: Formula,
  brandRules: Record<string, any>
): ValidationViolation[] {
  const violations: ValidationViolation[] = [];

  // Check mixing ratios
  for (const step of formula.steps) {
    const colorAmount = step.ingredients
      .filter(ing => ing.type === 'color')
      .reduce((sum, ing) => sum + ing.amount, 0);
    
    const developerAmount = step.ingredients
      .filter(ing => ing.type === 'developer')
      .reduce((sum, ing) => sum + ing.amount, 0);

    if (colorAmount > 0 && developerAmount > 0) {
      const ratio = developerAmount / colorAmount;
      
      if (ratio > brandRules.maxDeveloperRatio) {
        violations.push({
          type: 'brand',
          severity: 'error',
          message: `Proporción incorrecta para ${formula.brand}: ${ratio.toFixed(1)}:1. Máximo: ${brandRules.maxDeveloperRatio}:1`,
          step: step.id,
          suggestion: `Ajustar a proporción recomendada de ${formula.brand}`,
          autoFixAvailable: true,
        });
      }
    }
  }

  // Brand-specific special products validation
  validateSpecialProducts(formula, violations);

  // Developer compatibility
  validateDeveloperCompatibility(formula, violations, brandRules);

  return violations;
}
