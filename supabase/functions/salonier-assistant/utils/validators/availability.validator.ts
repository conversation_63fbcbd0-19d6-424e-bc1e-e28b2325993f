/**
 * Product Availability Validator
 * Validates product availability in inventory against formula requirements
 * Ensures all needed products (colors, developers, bleach, toners) are available
 */

import {
  Formula,
  ValidationViolation,
  ProductAvailability,
  ProcessType,
} from '../../types.ts';

/**
 * Validate product availability in inventory
 */
export function validateProductAvailability(formula: Formula): ValidationViolation[] {
  const violations: ValidationViolation[] = [];
  const availableProducts = formula.availableProducts || [];
  const levelDifference = formula.desiredLevel - formula.currentLevel;

  // Get brand and line availability data
  const brandProducts = availableProducts.filter(
    p => p.brand.toLowerCase() === formula.brand.toLowerCase()
  );
  const lineProducts = brandProducts.filter(
    p => p.line.toLowerCase() === formula.line.toLowerCase()
  );

  // 1. Validate bleach availability for high lift requirements
  if (levelDifference > 3 || formula.steps.some(step => step.type === ProcessType.BLEACHING)) {
    const hasDecolorante = lineProducts.some(p =>
      p.type === 'bleach' && p.hasDecolorante && p.isActive !== false
    );

    if (!hasDecolorante) {
      const alternativeLines = brandProducts
        .filter(p => p.type === 'bleach' && p.hasDecolorante)
        .map(p => p.line)
        .filter((line, index, self) => self.indexOf(line) === index);

      const alternativeBrands = availableProducts
        .filter(p => p.type === 'bleach' && p.hasDecolorante)
        .map(p => p.brand)
        .filter((brand, index, self) => self.indexOf(brand) === index);

      violations.push({
        type: 'availability',
        severity: 'critical',
        message: `La línea ${formula.line} no incluye decolorante - imposible aclarar ${levelDifference} niveles`,
        suggestion: alternativeLines.length > 0
          ? `Usar líneas de ${formula.brand}: ${alternativeLines.join(', ')}`
          : `Cambiar a marcas con decolorante: ${alternativeBrands.slice(0, 3).join(', ')}`,
        autoFixAvailable: alternativeLines.length > 0,
        alternativeLines,
        alternativeBrands,
      });
    }
  }

  // 2. Validate specific shade availability
  for (const step of formula.steps) {
    for (const ingredient of step.ingredients) {
      if (ingredient.type === 'color' && ingredient.shade) {
        const shadeAvailable = lineProducts.some(p =>
          p.type === 'color' &&
          p.availableShades?.includes(ingredient.shade) &&
          p.isActive !== false
        );

        if (!shadeAvailable) {
          const availableShadeNumbers = lineProducts
            .filter(p => p.type === 'color')
            .flatMap(p => p.availableShades || [])
            .map(shade => {
              const match = shade.match(/^(\d+)/);
              return match ? parseInt(match[1]) : 0;
            })
            .filter(num => num > 0);

          const maxShadeInLine = availableShadeNumbers.length > 0 ? Math.max(...availableShadeNumbers) : 0;

          const requestedLevel = parseInt(ingredient.shade!.match(/^(\d+)/)?.[1] || '0');
          const requestedReflect = ingredient.shade!.replace(/^\d+\.?/, '');

          let suggestion = '';
          if (requestedLevel > maxShadeInLine) {
            suggestion = `Tono ${ingredient.shade} no disponible - nivel máximo en esta línea es ${maxShadeInLine}`;
          } else if (requestedReflect) {
            suggestion = `Tono ${ingredient.shade} no disponible - verificar matices disponibles en nivel ${requestedLevel}`;
          }

          const alternativeLines = brandProducts
            .filter(p => p.availableShades?.includes(ingredient.shade!))
            .map(p => p.line)
            .filter((line, index, self) => self.indexOf(line) === index);

          violations.push({
            type: 'availability',
            severity: 'error',
            message: suggestion,
            step: step.id,
            ingredient: ingredient.product,
            suggestion: alternativeLines.length > 0
              ? `Disponible en líneas: ${alternativeLines.join(', ')}`
              : `Considerar tono similar disponible en ${formula.line}`,
            autoFixAvailable: false,
            alternativeLines,
          });
        }
      }
    }
  }

  // 3. Validate developer volume availability
  for (const step of formula.steps) {
    const developer = step.ingredients.find(ing => ing.type === 'developer');
    if (developer && developer.volume) {
      const volumeAvailable = lineProducts.some(p =>
        p.type === 'developer' &&
        p.availableDeveloperVolumes?.includes(developer.volume!) &&
        p.isActive !== false
      );

      if (!volumeAvailable) {
        const maxVolume = Math.max(
          ...lineProducts
            .filter(p => p.type === 'developer')
            .flatMap(p => p.availableDeveloperVolumes || [])
        );

        const alternativeBrands = availableProducts
          .filter(p => p.availableDeveloperVolumes?.includes(developer.volume!))
          .map(p => p.brand)
          .filter((brand, index, self) => self.indexOf(brand) === index);

        violations.push({
          type: 'availability',
          severity: 'error',
          message: `${formula.brand} no tiene oxidante ${developer.volume}vol - máximo disponible: ${maxVolume}vol`,
          step: step.id,
          ingredient: developer.product,
          suggestion: alternativeBrands.length > 0
            ? `Cambiar a marcas que tengan ${developer.volume}vol: ${alternativeBrands.slice(0, 3).join(', ')}`
            : `Usar ${maxVolume}vol disponible y ajustar proceso`,
          autoFixAvailable: maxVolume > 0,
          alternativeBrands,
        });
      }
    }
  }

  // 4. Validate toner availability for neutralization
  const needsToning = formula.steps.some(step =>
    step.type === ProcessType.TONING ||
    step.ingredients.some(ing => ing.type === 'toner')
  );

  if (needsToning) {
    const hasToners = lineProducts.some(p =>
      p.type === 'toner' && p.hasToners && p.isActive !== false
    );

    if (!hasToners) {
      const alternativeLines = brandProducts
        .filter(p => p.type === 'toner' && p.hasToners)
        .map(p => p.line)
        .filter((line, index, self) => self.indexOf(line) === index);

      violations.push({
        type: 'availability',
        severity: 'warning',
        message: `No hay matizadores disponibles en la línea ${formula.line}`,
        suggestion: alternativeLines.length > 0
          ? `Usar matizadores de: ${alternativeLines.join(', ')}`
          : 'Considerar otra marca con sistema de matizado',
        autoFixAvailable: alternativeLines.length > 0,
        alternativeLines,
      });
    }
  }

  // 5. Validate pre-pigmentation products
  const needsPrePigmentation = formula.steps.some(step =>
    step.type === ProcessType.PRE_PIGMENTATION
  );

  if (needsPrePigmentation) {
    const hasPrePigments = lineProducts.some(p =>
      p.type === 'pre-pigment' && p.hasPrePigmentation && p.isActive !== false
    );

    if (!hasPrePigments) {
      violations.push({
        type: 'availability',
        severity: 'warning',
        message: `No hay productos de pre-pigmentación en la línea ${formula.line}`,
        suggestion: 'Usar tonos cálidos de la misma marca como alternativa',
        autoFixAvailable: true,
      });
    }
  }

  // 6. Validate stock levels if available
  for (const step of formula.steps) {
    for (const ingredient of step.ingredients) {
      const matchingProduct = lineProducts.find(p =>
        p.type === ingredient.type &&
        (ingredient.shade ? p.availableShades?.includes(ingredient.shade) : true) &&
        (ingredient.volume ? p.availableDeveloperVolumes?.includes(ingredient.volume) : true)
      );

      if (matchingProduct && typeof matchingProduct.stock === 'number') {
        const requiredAmount = ingredient.amount || 0;
        if (matchingProduct.stock < requiredAmount) {
          violations.push({
            type: 'availability',
            severity: 'warning',
            message: `Stock insuficiente de ${ingredient.product}: disponible ${matchingProduct.stock}ml, necesario ${requiredAmount}ml`,
            step: step.id,
            ingredient: ingredient.product,
            suggestion: 'Verificar stock antes de comenzar el servicio',
            autoFixAvailable: false,
          });
        }
      }
    }
  }

  return violations;
}

/**
 * Correct availability violations by suggesting alternatives
 */
export function correctAvailabilityIssue(formula: Formula, violation: ValidationViolation): Formula {
  const corrected = { ...formula };

  // Auto-correct decolorante missing by suggesting alternative line
  if (violation.message.includes('no incluye decolorante') && violation.alternativeLines?.length) {
    corrected.line = violation.alternativeLines[0];
    corrected.warnings = [
      ...(corrected.warnings || []),
      `Línea cambiada a ${violation.alternativeLines[0]} para incluir decolorante`
    ];
  }

  // Auto-correct developer volume
  if (violation.message.includes('oxidante') && violation.step) {
    const step = corrected.steps.find(s => s.id === violation.step);
    if (step) {
      const developer = step.ingredients.find(ing => ing.type === 'developer');
      if (developer) {
        // Extract available volume from error message
        const maxVolumeMatch = violation.message.match(/máximo disponible: (\d+)vol/);
        if (maxVolumeMatch) {
          const maxVolume = parseInt(maxVolumeMatch[1]);
          developer.volume = maxVolume;
          corrected.warnings = [
            ...(corrected.warnings || []),
            `Volumen de oxidante ajustado a ${maxVolume}vol (máximo disponible)`
          ];
        }
      }
    }
  }

  return corrected;
}