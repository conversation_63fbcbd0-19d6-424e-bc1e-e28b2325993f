/**
 * Optimization Validator
 * Validates formula optimization opportunities (redundant steps, small amounts, etc.)
 */

import { Formula, ValidationViolation } from '../types';

/**
 * Validate optimization opportunities in formula
 */
export function validateOptimization(formula: Formula): ValidationViolation[] {
  const violations: ValidationViolation[] = [];

  // Check for redundant steps
  const stepTypes = formula.steps.map(step => step.type);
  const duplicateTypes = stepTypes.filter((type, index) => stepTypes.indexOf(type) !== index);

  if (duplicateTypes.length > 0) {
    violations.push({
      type: 'optimization',
      severity: 'warning',
      message: 'Pasos redundantes detectados',
      suggestion: 'Combinar pasos similares para mayor eficiencia',
      autoFixAvailable: true,
    });
  }

  // Check for inefficient product usage
  for (const step of formula.steps) {
    const totalIngredientAmount = step.ingredients.reduce((sum, ing) => sum + ing.amount, 0);

    if (totalIngredientAmount < 30) {
      violations.push({
        type: 'optimization',
        severity: 'warning',
        message: 'Cantidad muy pequeña puede ser difícil de mezclar uniformemente',
        step: step.id,
        suggestion: 'Considerar aumentar proporcionalmente todas las cantidades',
        autoFixAvailable: true,
      });
    }
  }

  return violations;
}
