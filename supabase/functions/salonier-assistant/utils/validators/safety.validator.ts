/**
 * Safety Validator
 * Validates safety considerations for hair formulas
 * Prevents dangerous chemical combinations and excessive processing
 */

import { Formula, ValidationViolation, ProcessType } from '../../types.ts';

/**
 * Validate safety considerations for a given formula
 */
export function validateSafety(formula: Formula): ValidationViolation[] {
  const violations: ValidationViolation[] = [];

  // Check for excessive processing times
  violations.push(...validateProcessingTime(formula));

  // Check for high volume developer on damaged hair
  violations.push(...validateDeveloperVolumeOnDamagedHair(formula));

  // Check for incompatible processes
  violations.push(...validateProcessOrder(formula));

  return violations;
}

/**
 * Validate total processing time doesn't exceed safe limits
 */
function validateProcessingTime(formula: Formula): ValidationViolation[] {
  const violations: ValidationViolation[] = [];

  if (formula.totalTime > 180) { // 3 hours
    violations.push({
      type: 'safety',
      severity: 'critical',
      message: `Tiempo total excesivo: ${formula.totalTime} minutos. Riesgo de daño severo`,
      suggestion: 'Dividir en múltiples sesiones',
      autoFixAvailable: false,
    });
  }

  return violations;
}

/**
 * Validate developer volume is appropriate for hair condition
 */
function validateDeveloperVolumeOnDamagedHair(formula: Formula): ValidationViolation[] {
  const violations: ValidationViolation[] = [];

  // Check for high volume developer on damaged hair
  if (formula.currentState === 'bleached') {
    for (const step of formula.steps) {
      const developer = step.ingredients.find(ing => ing.type === 'developer');
      if (developer && developer.volume && developer.volume > 20) {
        violations.push({
          type: 'safety',
          severity: 'error',
          message: 'Oxidante alto en cabello decolorado puede causar rotura',
          step: step.id,
          suggestion: 'Usar máximo 20 volúmenes en cabello dañado',
          autoFixAvailable: true,
        });
      }
    }
  }

  return violations;
}

/**
 * Validate the order of chemical processes is safe
 */
function validateProcessOrder(formula: Formula): ValidationViolation[] {
  const violations: ValidationViolation[] = [];

  // Check for incompatible processes
  const hasBleaching = formula.steps.some(step => step.type === ProcessType.BLEACHING);
  const hasColorRemoval = formula.steps.some(step => step.type === ProcessType.COLOR_REMOVAL);

  if (hasBleaching && hasColorRemoval) {
    const colorRemovalIndex = formula.steps.findIndex(step => step.type === ProcessType.COLOR_REMOVAL);
    const bleachingIndex = formula.steps.findIndex(step => step.type === ProcessType.BLEACHING);

    if (bleachingIndex <= colorRemovalIndex) {
      violations.push({
        type: 'safety',
        severity: 'error',
        message: 'Orden incorrecto: el decapado debe realizarse antes de la decoloración',
        suggestion: 'Reordenar pasos: decapado → evaluación → decoloración',
        autoFixAvailable: true,
      });
    }
  }

  return violations;
}