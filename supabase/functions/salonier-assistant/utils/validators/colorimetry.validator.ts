
import { Formula, ValidationViolation, ProcessType } from '../../types.ts';
import { COLORIMETRY_PRINCIPLES } from '../colorimetry-rules.ts';
import { calculateExpectedVolume } from '../helpers.ts';

export function validateColorimetryRules(
  formula: Formula,
): ValidationViolation[] {
  const violations: ValidationViolation[] = [];
  const levelDifference = formula.desiredLevel - formula.currentLevel;

  // Rule 1: Color cannot lift color
  if (levelDifference > 0 && formula.currentState === 'colored') {
    const hasColorRemoval = formula.steps.some(step => step.type === ProcessType.COLOR_REMOVAL);
    const hasDirectColorOnly = formula.steps.some(step => 
      step.type === ProcessType.DIRECT_COLOR && 
      !formula.steps.some(s => s.type === ProcessType.BLEACHING)
    );

    if (hasDirectColorOnly && !hasColorRemoval) {
      violations.push({
        type: 'colorimetry',
        severity: 'critical',
        message: 'Color no puede levantar color. Se requiere decapado previo o decoloración.',
        suggestion: 'Agregar paso de decapado antes de la coloración',
        autoFixAvailable: true,
      });
    }
  }

  // Rule 2: Inappropriate developer volume
  for (const step of formula.steps) {
    const developer = step.ingredients.find(ing => ing.type === 'developer');
    if (developer && developer.volume) {
      const expectedVolume = calculateExpectedVolume(formula, step.type);
      
      if (developer.volume > expectedVolume + 10) {
        violations.push({
          type: 'colorimetry',
          severity: 'error',
          message: `Volumen de oxidante demasiado alto: ${developer.volume}vol. Recomendado: ${expectedVolume}vol`,
          step: step.id,
          ingredient: developer.product,
          suggestion: `Reducir a ${expectedVolume} volúmenes`,
          autoFixAvailable: true,
        });
      }
    }
  }

  // Rule 3: Missing pre-pigmentation
  if (levelDifference < -COLORIMETRY_PRINCIPLES.PRE_PIGMENT_THRESHOLD) {
    const hasPrePigmentation = formula.steps.some(step => step.type === ProcessType.PRE_PIGMENTATION);
    
    if (!hasPrePigmentation) {
      violations.push({
        type: 'colorimetry',
        severity: 'error',
        message: `Pre-pigmentación requerida al oscurecer ${Math.abs(levelDifference)} niveles`,
        suggestion: 'Agregar paso de pre-pigmentación con pigmento cálido',
        autoFixAvailable: true,
      });
    }
  }

  // Rule 4: Excessive lift in single session
  if (levelDifference > COLORIMETRY_PRINCIPLES.MAX_LIFT_PER_SESSION) {
    violations.push({
      type: 'colorimetry',
      severity: 'critical',
      message: `Aclarado excesivo en una sesión: ${levelDifference} niveles. Máximo seguro: ${COLORIMETRY_PRINCIPLES.MAX_LIFT_PER_SESSION}`,
      suggestion: 'Dividir en múltiples sesiones',
      autoFixAvailable: false,
    });
  }

  // Rule 5: Gray coverage validation
  if (formula.grayPercentage && formula.grayPercentage > 50) {
    const hasNaturalBase = formula.steps.some(step =>
      step.ingredients.some(ing => 
        ing.shade?.includes('/0') || ing.shade?.includes('.0') || ing.shade?.includes('N')
      )
    );

    if (!hasNaturalBase) {
      violations.push({
        type: 'colorimetry',
        severity: 'warning',
        message: 'Más del 50% de canas requiere base natural en la mezcla',
        suggestion: 'Agregar 25-50% de tono natural base',
        autoFixAvailable: true,
      });
    }
  }

  return violations;
}
