// Salonier Assistant Edge Function - PERFORMANCE OPTIMIZED
// Version: 43 (Performance Optimized)
// Last Updated: 2025-09-16
// CRITICAL PERFORMANCE OPTIMIZATIONS (Target: 24.9s → <3s):
// ✅ #1: Reduced image processing timeout from 15s to 5s + stricter size limits (5MB max)
// ✅ #2: Removed global fetch override causing conflicts and unpredictable behavior
// ✅ #3: Implemented parallel processing for explanations + mixing (sequential → Promise.allSettled)
// ✅ #4: Added image caching system to prevent re-processing same URLs (10min TTL, 50 entries max)
//
// Previous Changes: Consolidated 6 Edge Functions into one central AI hub
// - Integrated chat_assistant functionality (from 4 chat functions)
// - Integrated upload_photo functionality
// - Maintained all existing capabilities: diagnose_image, analyze_desired_look, generate_formula
// - Single point of entry for all AI operations with centralized caching and authentication

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { generateQuickExplanation } from './utils/simple-explainer.ts';
import { getMixingSuggestion, suggestBasicMix } from './utils/basic-mixing.ts';

// CRITICAL FIX #2: Removed global fetch override that was causing conflicts and unpredictable behavior
// Using specific timeouts per operation instead of global 25s timeout

// Security utilities for safe logging
const securityUtils = {
  // Mask JWT tokens for logging
  maskJWT: (token: string): string => {
    if (!token) return '[EMPTY]';
    if (!token.startsWith('Bearer ') && !token.includes('.')) {
      return token; // Not a JWT, return as is
    }
    
    const bearerToken = token.startsWith('Bearer ') ? token.slice(7) : token;
    if (bearerToken.length < 16) return '[INVALID]';
    
    // Show first 8 chars + masked remainder
    return `${bearerToken.substring(0, 8)}***[${bearerToken.length - 8} chars masked]`;
  },
  
  // Sanitize payload data for logging
  sanitizeForLog: (payload: any): any => {
    if (!payload || typeof payload !== 'object') return payload;
    
    const sensitiveKeys = [
      'token', 'jwt', 'auth', 'authorization', 'bearer',
      'password', 'secret', 'key', 'credential'
    ];
    
    const sanitized = { ...payload };
    
    for (const key of Object.keys(sanitized)) {
      const lowercaseKey = key.toLowerCase();
      
      // Check if key contains sensitive terms
      if (sensitiveKeys.some(sensitive => lowercaseKey.includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
      
      // Recursively sanitize nested objects
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = securityUtils.sanitizeForLog(sanitized[key]);
      }
    }
    
    return sanitized;
  },
  
  // Generate safe preview without exposing sensitive data
  generateSafePreview: (data: any, maxLength: number = 200): string => {
    if (!data) return '[EMPTY]';
    
    const sanitized = securityUtils.sanitizeForLog(data);
    const preview = JSON.stringify(sanitized);
    
    if (preview.length <= maxLength) return preview;
    
    return `${preview.substring(0, maxLength)}...[${preview.length - maxLength} chars truncated]`;
  }
};

// Enhanced logger for Edge Functions with security compliance
const logger = {
  debug: (message: string, data?: any) => {
    // Sanitize data before logging
    const safeData = data ? securityUtils.sanitizeForLog(data) : '';
    console.debug(`[DEBUG] ${message}`, safeData);
  },
  info: (message: string, data?: any) => {
    // Sanitize data before logging
    const safeData = data ? securityUtils.sanitizeForLog(data) : '';
    console.log(`[INFO] ${message}`, safeData);
  },
  warn: (message: string, data?: any) => {
    const safeData = data ? securityUtils.sanitizeForLog(data) : '';
    console.warn(`[WARN] ${message}`, safeData);
  },
  error: (message: string, error?: any) => {
    // For errors, we still need useful debugging info but safely
    const safeError = error ? {
      message: error.message,
      name: error.name,
      stack: error.stack ? '[STACK TRACE AVAILABLE]' : null,
      code: error.code,
      status: error.status
    } : '';
    console.error(`[ERROR] ${message}`, safeError);
  },
  
  // Secure authentication logging
  authEvent: (message: string, context?: { userId?: string; salonId?: string; result: 'success' | 'failure' }) => {
    const sanitizedContext = context ? {
      userId: context.userId ? `user_${context.userId.substring(0, 8)}***` : '[NONE]',
      salonId: context.salonId ? `salon_${context.salonId.substring(0, 8)}***` : '[NONE]',
      result: context.result,
      timestamp: new Date().toISOString()
    } : null;
    console.log(`[AUTH] ${message}`, sanitizedContext);
  },
  
  // New diagnostic logger for detailed analysis results (already secure)
  diagnostic: (message: string, analysisResult?: any) => {
    if (analysisResult) {
      console.log(`[DIAGNOSTIC] ${message}`, {
        hairAnalysis: {
          averageLevel: analysisResult.averageLevel,
          overallTone: analysisResult.overallTone,
          overallReflect: analysisResult.overallReflect,
          hairThickness: analysisResult.hairThickness,
          hairDensity: analysisResult.hairDensity,
          overallCondition: analysisResult.overallCondition
        },
        zoneAnalysis: analysisResult.zoneAnalysis ? {
          roots: analysisResult.zoneAnalysis.roots,
          mids: analysisResult.zoneAnalysis.mids,
          ends: analysisResult.zoneAnalysis.ends
        } : null,
        confidence: analysisResult.overallConfidence,
        bucketInfo: analysisResult.bucketInfo,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log(`[DIAGNOSTIC] ${message}`);
    }
  }
};
import { PromptTemplates } from './utils/prompt-templates.ts';
import { OptimizedPrompts, PromptCache, FallbackResponses } from './utils/optimized-prompts.ts';
import { AIRouter, AIRequest as AIRouterRequest } from './utils/ai-router.ts';
import { ChemicalValidator } from './utils/chemical-validator.ts';
import { validateWithTimeout } from './utils/basic-validator.ts';
// TEMPORARILY DISABLED - CAUSING 504 TIMEOUTS
// import { FormulaExplainer, generateQuickExplanation } from './utils/formula-explainer.ts';
import {
  validateColorProcess,
  getColorimetryInstructions,
  ColorProcess,
  ProcessType,
} from './utils/colorimetry-rules.ts';
import {
  getBrandExpertise,
  getBrandValidationRules,
  getBrandExampleFormulas,
} from './utils/brand-expertise.ts';
// TEMPORARILY DISABLED - CAUSING 504 TIMEOUTS
// import { 
//   FormulaValidator, 
//   validateFormula, 
//   autoCorrectFormula,
//   type Formula,
//   type FormulaStep,
//   type FormulaIngredient,
//   type ValidationResult 
// } from './utils/formula-validator.ts';

// Category to type mapping - same as client-side
const CATEGORY_TO_TYPE_MAPPING: Record<string, string> = {
  // Spanish categories (UI) → English types (database)
  tinte: 'color',
  oxidante: 'developer',
  decolorante: 'bleach',
  tratamiento: 'treatment',
  matizador: 'toner',
  aditivo: 'additive',
  'pre-pigmentacion': 'pre_pigment',
  otro: 'other',

  // Additional Spanish variants that might come from AI
  colorante: 'color',
  oxigenada: 'developer',
  'agua oxigenada': 'developer',
  blanqueador: 'bleach',
  acondicionador: 'treatment',
  champú: 'treatment',
  champu: 'treatment',
  mascarilla: 'treatment',
  serum: 'treatment',
  aceite: 'treatment',

  // English types (for backwards compatibility)
  color: 'color',
  developer: 'developer',
  bleach: 'bleach',
  treatment: 'treatment',
  toner: 'toner',
  shampoo: 'treatment',
  conditioner: 'treatment',
  styling: 'treatment',
  additive: 'additive',
  pre_pigment: 'pre_pigment',
  other: 'other',
};

function mapCategoryToType(categoryOrType: string): string {
  const normalized = categoryOrType?.toLowerCase().trim() || '';
  return CATEGORY_TO_TYPE_MAPPING[normalized] || 'other';
}

// Extract bucket name from Supabase storage URL
function extractBucketFromUrl(imageUrl: string): string {
  try {
    const url = new URL(imageUrl);
    const pathMatch = url.pathname.match(/\/storage\/v1\/object\/(?:sign|public)\/([^\/]+)/);
    return pathMatch ? pathMatch[1] : 'unknown_bucket';
  } catch (error) {
    return 'invalid_url';
  }
}

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

interface AIRequest {
  task:
    | 'diagnose_image'
    | 'analyze_desired_look'
    | 'generate_formula'
    | 'convert_formula'
    | 'parse_product_text'
    | 'chat_assistant'
    | 'upload_photo';
  payload: Record<string, any>;
}

interface ChatRequest {
  conversationId: string;
  message: string;
  salonId: string;
  userId: string;
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    mimeType?: string;
  }>;
}

interface UploadRequest {
  imageBase64: string;
  salonId: string;
  clientId: string;
  photoType: 'before' | 'after' | 'desired';
  usePrivateBucket?: boolean;
}

interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

// Edge Function initialized

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// CRITICAL FIX #4: Image caching system to prevent re-processing same images
const imageCache = new Map<string, string>();
const IMAGE_CACHE_TTL = 10 * 60 * 1000; // 10 minutes TTL
const imageCacheTimestamps = new Map<string, number>();

function getCachedImage(url: string): string | null {
  const cached = imageCache.get(url);
  const timestamp = imageCacheTimestamps.get(url);

  if (cached && timestamp && Date.now() - timestamp < IMAGE_CACHE_TTL) {
    console.log('✅ Image cache hit for URL');
    return cached;
  }

  // Clean up expired entries
  if (timestamp && Date.now() - timestamp >= IMAGE_CACHE_TTL) {
    imageCache.delete(url);
    imageCacheTimestamps.delete(url);
  }

  return null;
}

function setCachedImage(url: string, base64Data: string): void {
  // Limit cache size to prevent memory issues
  if (imageCache.size >= 50) {
    // Remove oldest entry
    const oldestUrl = Array.from(imageCacheTimestamps.entries())
      .sort(([,a], [,b]) => a - b)[0][0];
    imageCache.delete(oldestUrl);
    imageCacheTimestamps.delete(oldestUrl);
  }

  imageCache.set(url, base64Data);
  imageCacheTimestamps.set(url, Date.now());
  console.log(`📦 Cached image (cache size: ${imageCache.size})`);
}

// Cache categories for TTL management
const CACHE_CATEGORIES = {
  exact_match: { ttl: 30 * 24 * 60 * 60 * 1000, priority: 1 }, // 30 days
  similar_case: { ttl: 7 * 24 * 60 * 60 * 1000, priority: 2 },  // 7 days
  template: { ttl: 3 * 24 * 60 * 60 * 1000, priority: 3 }       // 3 days
};

// Fields that affect AI output (must be included in cache key)
const CACHE_RELEVANT_FIELDS = {
  diagnose_image: ['imageHash', 'imageUrl', 'analysis_depth', 'language'],
  analyze_desired_look: ['imageHash', 'imageUrl', 'currentLevel', 'diagnosis', 'language'],
  generate_formula: ['diagnosis', 'desiredResult', 'brand', 'line', 'language'],
  convert_formula: ['formulaText', 'fromBrand', 'toBrand', 'language'],
  parse_product_text: ['text', 'language'],
  chat_assistant: ['message', 'context', 'conversation_id', 'language']
};

// Fields to exclude from cache key (metadata, timestamps, etc.)
const CACHE_EXCLUDED_FIELDS = [
  'timestamp', 'created_at', 'updated_at', 'request_id', 'session_id',
  'user_id', 'salon_id', 'device_info', 'app_version', 'ip_address'
];

// Deterministic normalization function
function normalizeValue(value: any): any {
  if (value === null || value === undefined) {
    return null;
  }
  
  if (typeof value === 'string') {
    return value.toLowerCase().trim();
  }
  
  if (typeof value === 'number') {
    return Math.round(value * 100) / 100; // Round to 2 decimal places
  }
  
  if (typeof value === 'boolean') {
    return value;
  }
  
  if (Array.isArray(value)) {
    return value
      .map(normalizeValue)
      .sort((a, b) => JSON.stringify(a).localeCompare(JSON.stringify(b)));
  }
  
  if (typeof value === 'object') {
    const normalized: any = {};
    const sortedKeys = Object.keys(value).sort();
    
    for (const key of sortedKeys) {
      const normalizedKey = key.toLowerCase().trim();
      if (!CACHE_EXCLUDED_FIELDS.includes(normalizedKey)) {
        normalized[normalizedKey] = normalizeValue(value[key]);
      }
    }
    
    return normalized;
  }
  
  return value;
}

// Extract relevant fields for cache key
function extractRelevantFields(task: string, payload: any): any {
  const relevantFields = CACHE_RELEVANT_FIELDS[task as keyof typeof CACHE_RELEVANT_FIELDS] || [];
  const extracted: any = {};
  
  // Include task-specific relevant fields
  for (const field of relevantFields) {
    if (payload[field] !== undefined) {
      extracted[field] = payload[field];
    }
  }
  
  // For image tasks, create consistent hash from image data
  if (payload.imageBase64) {
    // Use first 200 chars + length for consistent hashing
    const imageData = payload.imageBase64;
    extracted.imageHash = `${imageData.substring(0, 200)}_len:${imageData.length}`;
    delete extracted.imageUrl; // Prefer hash over URL
  }
  
  return extracted;
}

// Generate cache category based on payload similarity
function getCacheCategory(task: string, payload: any): keyof typeof CACHE_CATEGORIES {
  const extracted = extractRelevantFields(task, payload);
  
  // Exact match: all relevant fields present and specific
  if (task === 'generate_formula' && extracted.diagnosis && extracted.desiredResult && extracted.brand) {
    return 'exact_match';
  }
  
  if (task === 'diagnose_image' && (extracted.imageHash || extracted.imageUrl)) {
    return 'exact_match';
  }
  
  // Similar case: partial match with key fields
  if (task === 'generate_formula' && extracted.diagnosis && extracted.desiredResult) {
    return 'similar_case';
  }
  
  // Template: generic cases
  return 'template';
}

// Enhanced cache key generation with deterministic normalization
function generateCacheKey(task: string, payload: any): string {
  try {
    // 1. Extract only relevant fields
    const relevantData = extractRelevantFields(task, payload);
    
    // 2. Normalize values deterministically
    const normalized = normalizeValue(relevantData);
    
    // 3. Create stable JSON representation
    const stableJson = JSON.stringify(normalized, Object.keys(normalized).sort());
    
    // 4. Generate hash
    const encoder = new TextEncoder();
    const data = encoder.encode(`${task}:${stableJson}`);
    
    // 5. Create base64 hash with category prefix
    const category = getCacheCategory(task, payload);
    const hash = btoa(String.fromCharCode(...new Uint8Array(data)));
    
    return `${category}:${hash}`;
  } catch (error) {
    // Fallback to simple hash if normalization fails
    logger.warn('Cache key generation failed, using fallback', { task, error: error.message });
    const simple = JSON.stringify({ task, timestamp: Date.now() });
    return btoa(simple);
  }
}

// Enhanced cache checking with similarity search
async function checkCache(salonId: string, task: string, inputHash: string): Promise<any | null> {
  try {
    // 1. Try exact match first
    const { data: exactMatch, error: exactError } = await supabase
      .from('ai_analysis_cache')
      .select('result, cache_category, created_at, tokens_used, cost_usd')
      .eq('salon_id', salonId)
      .eq('analysis_type', task)
      .eq('input_hash', inputHash)
      .gte('expires_at', new Date().toISOString())
      .single();

    if (!exactError && exactMatch) {
      logger.debug('Cache hit: exact match', { 
        task, 
        category: exactMatch.cache_category,
        age: Math.round((Date.now() - new Date(exactMatch.created_at).getTime()) / 1000 / 60) + ' minutes'
      });
      return exactMatch.result;
    }

    // 2. Try similar matches for high-value tasks
    if (task === 'generate_formula' || task === 'diagnose_image') {
      const category = inputHash.split(':')[0];
      
      // Look for similar cases in the same category
      const { data: similarMatches, error: similarError } = await supabase
        .from('ai_analysis_cache')
        .select('result, input_hash, cache_category, created_at')
        .eq('salon_id', salonId)
        .eq('analysis_type', task)
        .eq('cache_category', category)
        .gte('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(5);

      if (!similarError && similarMatches && similarMatches.length > 0) {
        // For now, just log potential matches - could implement fuzzy matching here
        logger.debug('Found similar cache entries', { 
          task, 
          count: similarMatches.length,
          category 
        });
        
        // Could implement string similarity comparison here
        // For now, return null to proceed with new AI request
      }
    }

    return null;
  } catch (error) {
    logger.warn('Cache check failed', { task, error: error.message });
    return null;
  }
}

// Enhanced cache saving with intelligent TTL and cleanup
async function saveToCache(
  salonId: string,
  task: string,
  inputHash: string,
  inputData: any,
  result: any,
  model: string,
  tokensUsed: number,
  costUsd: number
) {
  try {
    // Extract category from cache key
    const category = inputHash.split(':')[0] as keyof typeof CACHE_CATEGORIES;
    const cacheConfig = CACHE_CATEGORIES[category] || CACHE_CATEGORIES.template;
    
    // Calculate smart expiration based on category
    const expiresAt = new Date(Date.now() + cacheConfig.ttl).toISOString();
    
    // Prepare cache entry
    const cacheEntry = {
      salon_id: salonId,
      analysis_type: task,
      input_hash: inputHash,
      input_data: inputData,
      result: result,
      model_used: model,
      tokens_used: tokensUsed,
      cost_usd: costUsd,
      cache_category: category,
      cache_priority: cacheConfig.priority,
      created_at: new Date().toISOString(),
      expires_at: expiresAt,
    };

    // Save cache entry
    const { error } = await supabase
      .from('ai_analysis_cache')
      .upsert(cacheEntry);

    if (error) {
      logger.warn('Failed to save cache entry', { task, error: error.message });
    } else {
      logger.debug('Cache entry saved', { 
        task, 
        category, 
        ttl: Math.round(cacheConfig.ttl / 1000 / 60 / 60) + 'h',
        tokens: tokensUsed,
        cost: costUsd.toFixed(4) 
      });
    }

    // Periodically clean up expired entries (5% chance)
    if (Math.random() < 0.05) {
      cleanupExpiredCache(salonId);
    }

  } catch (error) {
    logger.error('Cache save operation failed', { task, salonId, error });
  }
}

// Background cleanup of expired cache entries
async function cleanupExpiredCache(salonId: string) {
  try {
    const { error } = await supabase
      .from('ai_analysis_cache')
      .delete()
      .eq('salon_id', salonId)
      .lt('expires_at', new Date().toISOString());

    if (!error) {
      logger.debug('Cache cleanup completed');
    }
  } catch (error) {
    logger.warn('Cache cleanup failed', { error: error.message });
  }
}

// Cache analytics for performance monitoring
async function getCacheStats(salonId: string): Promise<any> {
  try {
    const { data, error } = await supabase
      .from('ai_analysis_cache')
      .select('analysis_type, cache_category, created_at, tokens_used, cost_usd')
      .eq('salon_id', salonId)
      .gte('expires_at', new Date().toISOString());

    if (error || !data) {
      return { totalEntries: 0, totalSavings: { tokens: 0, cost: 0 } };
    }

    const stats = {
      totalEntries: data.length,
      byTask: {} as Record<string, number>,
      byCategory: {} as Record<string, number>,
      totalSavings: {
        tokens: data.reduce((sum, entry) => sum + (entry.tokens_used || 0), 0),
        cost: data.reduce((sum, entry) => sum + (entry.cost_usd || 0), 0)
      },
      oldestEntry: data.length > 0 ? Math.min(...data.map(entry => 
        Math.round((Date.now() - new Date(entry.created_at).getTime()) / 1000 / 60)
      )) : 0
    };

    // Group by task and category
    data.forEach(entry => {
      stats.byTask[entry.analysis_type] = (stats.byTask[entry.analysis_type] || 0) + 1;
      if (entry.cache_category) {
        stats.byCategory[entry.cache_category] = (stats.byCategory[entry.cache_category] || 0) + 1;
      }
    });

    return stats;
  } catch (error) {
    logger.warn('Failed to get cache stats', { error: error.message });
    return { totalEntries: 0, totalSavings: { tokens: 0, cost: 0 } };
  }
}

// String similarity calculation for fuzzy matching
function calculateStringSimilarity(str1: string, str2: string): number {
  if (str1 === str2) return 1.0;
  
  const len1 = str1.length;
  const len2 = str2.length;
  
  if (len1 === 0) return len2 === 0 ? 1.0 : 0.0;
  if (len2 === 0) return 0.0;
  
  // Simple Levenshtein distance calculation
  const matrix = Array(len2 + 1).fill(null).map(() => Array(len1 + 1).fill(null));
  
  for (let i = 0; i <= len1; i++) {
    matrix[0][i] = i;
  }
  
  for (let j = 0; j <= len2; j++) {
    matrix[j][0] = j;
  }
  
  for (let j = 1; j <= len2; j++) {
    for (let i = 1; i <= len1; i++) {
      if (str1[i - 1] === str2[j - 1]) {
        matrix[j][i] = matrix[j - 1][i - 1];
      } else {
        matrix[j][i] = Math.min(
          matrix[j - 1][i - 1] + 1,     // substitution
          matrix[j][i - 1] + 1,         // insertion
          matrix[j - 1][i] + 1          // deletion
        );
      }
    }
  }
  
  const maxLen = Math.max(len1, len2);
  return (maxLen - matrix[len2][len1]) / maxLen;
}

// Helper for retry with exponential backoff
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;

      // Check if it's a rate limit error
      if (error.status === 429 || error.message?.includes('rate limit')) {
        const delay = initialDelay * Math.pow(2, i);
        // Rate limited - retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        // If it's not a rate limit error, throw immediately
        throw error;
      }
    }
  }

  throw lastError;
}

// Helper to extract JSON from AI response that might include markdown formatting
function extractJsonFromString(text: string): string {
  if (!text || typeof text !== 'string') {
    throw new Error('Input text is empty or not a string');
  }

  // Clean the response by removing common markdown formatting
  let cleanText = text.trim();

  // Remove markdown code blocks if present
  if (cleanText.includes('```json')) {
    // Cleaning markdown JSON block
    cleanText = cleanText.replace(/```json\s*/g, '').replace(/```/g, '');
  } else if (cleanText.includes('```')) {
    // Cleaning generic markdown block
    cleanText = cleanText.replace(/```\s*/g, '');
  }

  // Find JSON object boundaries
  const startIndex = cleanText.indexOf('{');
  const endIndex = cleanText.lastIndexOf('}');

  if (startIndex === -1 || endIndex === -1) {
    throw new Error('No JSON object boundaries found in the string');
  }

  if (endIndex <= startIndex) {
    throw new Error('Invalid JSON object boundaries (end before start)');
  }

  // Extract the JSON substring
  const jsonString = cleanText.substring(startIndex, endIndex + 1);

  // Basic validation: ensure it looks like valid JSON
  if (!jsonString.startsWith('{') || !jsonString.endsWith('}')) {
    throw new Error('Extracted string does not appear to be a valid JSON object');
  }

  // JSON extraction completed

  return jsonString;
}

// Helper to convert image URL to base64
async function urlToBase64(url: string): Promise<string> {
  try {
    // CRITICAL FIX #4: Check image cache first
    const cachedImage = getCachedImage(url);
    if (cachedImage) {
      return cachedImage;
    }

    logger.debug('Converting URL to base64', {
      url: url.substring(0, 150) + '...',
      urlLength: url.length
    });

    // CRITICAL FIX #1: Reduced image processing timeout from 15s to 5s
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.error('⚠️ Image download timeout after 5s - aborting');
      controller.abort();
    }, 5000); // 5 second timeout (reduced from 15s)

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'User-Agent': 'Salonier-Assistant/1.0',
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        logger.error('Failed to fetch image from URL', {
          status: response.status,
          statusText: response.statusText,
          url: url.substring(0, 100) + '...'
        });
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      logger.debug('Image fetch successful', {
        contentType,
        contentLength: response.headers.get('content-length')
      });

      const arrayBuffer = await response.arrayBuffer();
      const bytes = new Uint8Array(arrayBuffer);

      // CRITICAL FIX #1: Stricter image size validation for faster processing
      if (bytes.length > 5 * 1024 * 1024) {
        throw new Error(`Image too large: ${(bytes.length / 1024 / 1024).toFixed(2)}MB (max 5MB for faster processing)`);
      }

      logger.debug('Converting arrayBuffer to base64', { 
        imageSize: bytes.length,
        imageSizeMB: (bytes.length / 1024 / 1024).toFixed(2)
      });

      // More efficient base64 conversion
      let base64 = '';
      const chunkSize = 8192;
      for (let i = 0; i < bytes.length; i += chunkSize) {
        const chunk = bytes.subarray(i, i + chunkSize);
        base64 += String.fromCharCode(...chunk);
      }
      
      const encodedBase64 = btoa(base64);
      
      // Determine MIME type from content-type or default to jpeg
      let mimeType = 'image/jpeg';
      if (contentType && contentType.startsWith('image/')) {
        mimeType = contentType;
      }

      const dataUrl = `data:${mimeType};base64,${encodedBase64}`;
      
      logger.debug('URL to base64 conversion completed', {
        originalSize: bytes.length,
        base64Size: encodedBase64.length,
        dataUrlSize: dataUrl.length,
        mimeType
      });

      // CRITICAL FIX #4: Cache the processed image
      setCachedImage(url, dataUrl);

      return dataUrl;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }
  } catch (error) {
    logger.error('URL to base64 conversion failed', {
      error: error.message,
      url: url.substring(0, 100) + '...',
      errorType: error.constructor.name
    });
    
    // Provide more specific error messages
    if (error.name === 'AbortError') {
      throw new Error('Image download timeout - please try with a smaller image or check your connection');
    } else if (error.message.includes('HTTP 403') || error.message.includes('HTTP 401')) {
      throw new Error('Image URL access denied - the signed URL may have expired');
    } else if (error.message.includes('HTTP 404')) {
      throw new Error('Image not found - the URL may be invalid or the image was deleted');
    } else if (error.message.includes('too large')) {
      throw error; // Pass through size error as-is
    } else {
      throw new Error(`Failed to process image URL: ${error.message}`);
    }
  }
}

// Helper to ensure user has salon_id (auto-repair legacy profiles)
async function ensureUserHasSalonId(userId: string): Promise<string | null> {
  // Checking user salon_id

  try {
    // First check if user already has salon_id
    const { data: profile } = await supabase
      .from('profiles')
      .select('salon_id')
      .eq('id', userId)
      .single();

    if (profile?.salon_id) {
      // User already has salon_id
      return profile.salon_id;
    }

    // If not, try to find their salon through team members
    // User profile missing salon_id, attempting to repair

    const { data: teamMember } = await supabase
      .from('team_members')
      .select('salon_id')
      .eq('user_id', userId)
      .single();

    if (teamMember?.salon_id) {
      // Update profile with found salon_id
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ salon_id: teamMember.salon_id })
        .eq('id', userId);

      if (updateError) {
        // Profile update failed
        return null;
      }

      // Profile repaired with salon_id from team_members
      return teamMember.salon_id;
    }

    // As last resort, check if user owns a salon
    const { data: salon } = await supabase
      .from('salons')
      .select('id')
      .eq('owner_id', userId)
      .single();

    if (salon?.id) {
      // Update profile with owned salon_id
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ salon_id: salon.id })
        .eq('id', userId);

      if (updateError) {
        // Profile update with owned salon failed
        return null;
      }

      // Profile repaired with owned salon_id
      return salon.id;
    }

    // Salon ID not found for user
    return null;
  } catch (error) {
    // Error ensuring user has salon ID
    return null;
  }
}

// Updated pricing as of 2025
const MODEL_PRICING = {
  'gpt-4o': { input: 2.5, output: 10.0 }, // per 1M tokens
  'gpt-4o-mini': { input: 0.15, output: 0.6 }, // per 1M tokens
  'gpt-3.5-turbo': { input: 0.5, output: 1.5 }, // per 1M tokens
};

function calculateCost(model: string, inputTokens: number, outputTokens: number): number {
  const pricing = MODEL_PRICING[model as keyof typeof MODEL_PRICING] || MODEL_PRICING['gpt-4o'];
  const inputCost = (inputTokens / 1_000_000) * pricing.input;
  const outputCost = (outputTokens / 1_000_000) * pricing.output;
  return inputCost + outputCost;
}

// Helper function to determine complexity based on context
function determineComplexity(diagnosis?: any): 'simple' | 'medium' | 'complex' {
  // Simple cases: virgin hair, basic touch-ups, no previous treatments
  if (!diagnosis) return 'simple';

  const hasChemicalProcess =
    diagnosis.detectedChemicalProcess &&
    diagnosis.detectedChemicalProcess !== 'Ninguno' &&
    diagnosis.detectedChemicalProcess !== 'None';

  const hasHighDamage =
    diagnosis.overallCondition?.includes('Severo') ||
    diagnosis.overallCondition?.includes('Severe') ||
    diagnosis.damage === 'Severo' ||
    diagnosis.damage === 'Severe';

  const hasMetallicRisk = diagnosis.detectedRisks?.metallic === true;
  const hasHennaRisk = diagnosis.detectedRisks?.henna === true;

  // Complex cases need premium model
  if (hasMetallicRisk || hasHennaRisk || hasHighDamage) {
    return 'complex';
  }

  // Medium complexity for previously treated hair
  if (hasChemicalProcess) {
    return 'medium';
  }

  // Simple for virgin hair or basic services
  return 'simple';
}

// Task handlers
async function diagnoseImage(payload: any, salonId: string): Promise<AIResponse> {
  const { imageUrl, imageBase64, diagnosis } = payload;

  // Enhanced Debug: Log what diagnoseImage received
  logger.debug('diagnoseImage called with payload', {
    hasImageUrl: !!imageUrl,
    hasImageBase64: !!imageBase64,
    hasDiagnosis: !!diagnosis,
    imageUrlLength: imageUrl ? imageUrl.length : 0,
    imageUrlPreview: imageUrl ? imageUrl.substring(0, 100) + '...' : null,
    imageBase64Length: imageBase64 ? imageBase64.length : 0,
    payloadKeys: Object.keys(payload),
    salonId: salonId
  });

  // Validate that we have image data
  if (!imageUrl && !imageBase64) {
    logger.error('No image data provided', { payload });
    throw new Error('No image data provided. Either imageUrl or imageBase64 is required.');
  }

  // Soportar ambos formatos para compatibilidad
  let imageDataUrl: string;

  if (imageUrl) {
    // Convertir URL (privada firmada o pública) a base64 para OpenAI
    // Converting URL (private signed or public) to base64 for diagnosis
    logger.debug('Attempting to convert URL to base64', { url: imageUrl.substring(0, 100) + '...' });
    try {
      imageDataUrl = await urlToBase64(imageUrl);
      logger.debug('URL to base64 conversion successful', { base64Length: imageDataUrl.length });
    } catch (error) {
      logger.error('URL to base64 conversion failed', error);
      
      // If we have both imageUrl and imageBase64, fall back to base64
      if (imageBase64) {
        logger.warn('Falling back to base64 due to URL processing failure');
        const cleanBase64 = imageBase64.replace(/\s/g, '').replace(/\n/g, '');
        const base64SizeInBytes = (cleanBase64.length * 3) / 4;
        
        if (base64SizeInBytes > 4 * 1024 * 1024) {
          throw new Error('Image too large. Maximum size is 4MB');
        }
        
        imageDataUrl = cleanBase64.startsWith('data:') ? cleanBase64 : `data:image/jpeg;base64,${cleanBase64}`;
        logger.debug('Using fallback base64', { base64Length: imageDataUrl.length });
      } else {
        throw new Error(`Failed to process image URL: ${error.message}`);
      }
    }
  } else if (imageBase64) {
    // Mantener compatibilidad con base64
    // Using base64 for backward compatibility

    // Validar que imageBase64 no sea null o undefined
    if (!imageBase64) {
      // Image data is empty
      throw new Error('Image base64 is empty');
    }

    // Validar y limpiar base64
    const cleanBase64 = imageBase64.replace(/\s/g, '').replace(/\n/g, '');

    // Verificar tamaño del base64
    const base64SizeInBytes = (cleanBase64.length * 3) / 4;
    // Base64 size validated

    // Limitar tamaño máximo a ~4MB después de decodificar
    if (base64SizeInBytes > 4 * 1024 * 1024) {
      // Image size exceeds limit
      throw new Error('Image too large. Maximum size is 4MB');
    }

    // Validar que sea base64 válido
    try {
      // Verificar que solo tenga caracteres válidos de base64
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
        // Invalid base64 format
        throw new Error('Invalid base64 format');
      }
      imageDataUrl = `data:image/jpeg;base64,${cleanBase64}`;
    } catch (e) {
      // Base64 validation failed
      throw new Error('Invalid image format');
    }
  } else {
    // No image data provided
    logger.error('No image data provided in diagnoseImage', {
      receivedPayload: JSON.stringify(payload),
      hasImageUrl: !!imageUrl,
      hasImageBase64: !!imageBase64,
      imageUrlType: typeof imageUrl,
      imageBase64Type: typeof imageBase64,
      imageUrlValue: imageUrl,
      imageBase64Preview: imageBase64 ? imageBase64.substring(0, 50) + '...' : null
    });
    throw new Error('No image provided');
  }

  // Determine complexity based on image and context
  const complexity = determineComplexity(diagnosis);

  // Use FULL prompt to get ALL required fields including:
  // - elasticity, resistance, cuticleState
  // - grayType, grayPattern
  // - unwantedTone, estimatedLastProcessDate
  // - ALL zone analysis fields for roots, mids, ends
  const prompt = PromptTemplates.getDiagnosisPrompt('full', 'es');

  // Using optimized prompt for diagnosis
  // Using complexity level
  // Prompt optimized

  try {
    // Preparing OpenAI request
    // Image data URL validated
    // Image data URL format checked

    // Validar que imageDataUrl existe y tiene el formato correcto
    if (!imageDataUrl) {
      throw new Error('Image data URL is undefined');
    }

    // Validar que sea data URL después de conversión
    if (!imageDataUrl.startsWith('data:image/')) {
      throw new Error(
        `Invalid image format. Expected data URL after conversion. Got: ${imageDataUrl.substring(0, 30)}`
      );
    }

    // Construir el mensaje de manera segura
    const messageContent = [
      { type: 'text', text: prompt },
      { type: 'image_url', image_url: { url: imageDataUrl, detail: 'high' } },
    ];

    // Message content structure validated

    // IMPORTANT: Always use vision-capable model for image analysis
    // gpt-3.5-turbo does NOT support images
    // Vision models do NOT support functions/function_call - only response_format
    const requestBody = {
      model: 'gpt-4o-mini', // Always use vision model for images
      messages: [
        {
          role: 'user',
          content: messageContent,
        },
      ],
      max_tokens: 1500,
      temperature: 0,
      top_p: 1,
      seed: 42,
      response_format: { type: 'json_object' },
      // Note: Vision models do NOT support functions/function_call
      // Using only response_format for structured output
    };

    // Sending request to OpenAI
    // OpenAI API key verified

    if (!openaiApiKey) {
      throw new Error('OpenAI API key is not configured');
    }

    const response = await retryWithBackoff(async () => {
      const headers = {
        Authorization: `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      };
    // Request headers prepared

      // Create timeout controller (25s to stay under Supabase 30s limit)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.error('OpenAI request timeout after 25 seconds');
        controller.abort();
      }, 25000);

      try {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers,
          body: JSON.stringify(requestBody),
          signal: controller.signal,
        });
        clearTimeout(timeoutId);
        return response;
      } catch (error: any) {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
          throw new Error('OpenAI request timeout after 25 seconds. Please try again.');
        }
        throw error;
      }
    });

    // Check HTTP status first
    if (!response.ok) {
      // OpenAI API request failed

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      // Error response received

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();
    // OpenAI response received

    if (data.error) {
      // OpenAI API error in response
      // Image URL included in failed request
      throw new Error(data.error.message || 'OpenAI API error');
    }

    // Validate response structure with detailed logging
    // Validating OpenAI response
    // Response structure validated
    // Choices validated

    if (!data.choices) {
      // OpenAI response missing choices
      throw new Error('OpenAI response missing choices - possible API quota issue');
    }

    if (!Array.isArray(data.choices) || data.choices.length === 0) {
      // Invalid choices array in response
      throw new Error('OpenAI response has empty or invalid choices array');
    }

    const choice = data.choices[0];
    // First choice structure validated

    if (!choice.message) {
      // Choice missing message content
      throw new Error('OpenAI response choice missing message');
    }

    if (!choice.message.content) {
      // Message missing content field
    // Message structure validated
    // Finish reason processed

      // Check for content moderation or refusal
      if (choice.message.refusal) {
        // OpenAI refused to process request
        throw new Error(`OpenAI rechazó procesar la imagen: ${choice.message.refusal}`);
      }

      if (choice.finish_reason === 'content_filter') {
        throw new Error(
          'La imagen fue bloqueada por filtros de contenido. Por favor, usa una foto que muestre solo el cabello.'
        );
      }

      if (choice.finish_reason === 'length') {
        throw new Error('La respuesta fue truncada. Por favor, intenta con una imagen más simple.');
      }

      // Generic error with more context
      throw new Error(
        `OpenAI no pudo procesar la imagen (${choice.finish_reason || 'razón desconocida'}). Por favor, intenta con otra foto del cabello.`
      );
    }

    // OpenAI response structure validated

    // Parse AI response with error handling
    let result;
    const aiResponse = data.choices[0].message.content;

    try {
      // Log first 200 chars of response for debugging
    // AI response preview available

      // Try to extract JSON if AI added extra text
      let jsonStr = aiResponse.trim();

      // Remove markdown code blocks if present
      if (jsonStr.includes('```json')) {
        jsonStr = jsonStr.replace(/```json\s*/g, '').replace(/```/g, '');
      }

      // Find JSON object boundaries
      const jsonStart = jsonStr.indexOf('{');
      const jsonEnd = jsonStr.lastIndexOf('}') + 1;

      if (jsonStart !== -1 && jsonEnd > jsonStart) {
        jsonStr = jsonStr.substring(jsonStart, jsonEnd);
      }

      result = JSON.parse(jsonStr);
    // AI response parsed successfully
    } catch (parseError) {
      // AI response parsing failed
      throw new Error(`Invalid JSON from AI: ${parseError.message}`);
    }

    // Validate required fields in result
    if (!result || typeof result !== 'object') {
      // AI response format invalid
      throw new Error('AI response is not a valid object');
    }

    // With full prompt, AI should return complete structure directly
    // Only validate that we have the expected structure
    if (!result.zoneAnalysis || !result.hairThickness || !result.hairDensity) {
      // AI returned incomplete structure
    // Result structure analyzed
    } else {
    // AI analysis structure complete
    }

    // Mapeo de compatibilidad para términos antiguos
    if (result) {
      // Mapear términos generales si vienen con nombres antiguos
      if (result.overallUndertone && !result.overallReflect) {
        result.overallReflect = result.overallUndertone;
        delete result.overallUndertone;
      }
      if (result.averageDepthLevel && !result.averageLevel) {
        result.averageLevel = result.averageDepthLevel;
        delete result.averageDepthLevel;
      }

      // Mapear términos en análisis por zonas
      if (result.zoneAnalysis) {
        ['roots', 'mids', 'ends'].forEach(zone => {
          if (result.zoneAnalysis[zone]) {
            // Mapear depth a level
            if (result.zoneAnalysis[zone].depth && !result.zoneAnalysis[zone].level) {
              result.zoneAnalysis[zone].level = result.zoneAnalysis[zone].depth;
              delete result.zoneAnalysis[zone].depth;
            }
            // Mapear undertone a reflect
            if (result.zoneAnalysis[zone].undertone && !result.zoneAnalysis[zone].reflect) {
              result.zoneAnalysis[zone].reflect = result.zoneAnalysis[zone].undertone;
              delete result.zoneAnalysis[zone].undertone;
            }
          }
        });
      }
    }

    // Calculate token usage and cost before logging
    const inputTokens = data.usage?.prompt_tokens || 0;
    const outputTokens = data.usage?.completion_tokens || 0;
    const totalTokens = data.usage?.total_tokens || 0;
    const costUsd = calculateCost('gpt-4o-mini', inputTokens, outputTokens);

    // DIAGNOSTIC LOGGING: Always log detailed analysis results
    logger.diagnostic('AI Diagnosis Analysis Completed Successfully', {
      ...result,
      bucketInfo: imageUrl ? {
        bucket: extractBucketFromUrl(imageUrl),
        isSignedUrl: imageUrl.includes('token=')
      } : { source: 'base64_upload' },
      tokensUsed: totalTokens,
      costUsd: costUsd.toFixed(4),
      modelUsed: 'gpt-4o-mini',
      complexity: complexity
    });

    // Also log with standard logger for backward compatibility
    logger.info('AI Diagnosis Analysis Completed Successfully', {
      bucket: imageUrl ? extractBucketFromUrl(imageUrl) : 'base64_upload',
      hairAnalysis: {
        averageLevel: result.averageLevel,
        overallTone: result.overallTone,
        overallReflect: result.overallReflect,
        hairThickness: result.hairThickness,
        hairDensity: result.hairDensity,
        overallCondition: result.overallCondition
      },
      zoneAnalysis: {
        roots: {
          level: result.zoneAnalysis?.roots?.level,
          tone: result.zoneAnalysis?.roots?.tone,
          reflect: result.zoneAnalysis?.roots?.reflect,
          confidence: result.zoneAnalysis?.roots?.confidence
        },
        mids: {
          level: result.zoneAnalysis?.mids?.level,
          tone: result.zoneAnalysis?.mids?.tone,
          reflect: result.zoneAnalysis?.mids?.reflect,
          confidence: result.zoneAnalysis?.mids?.confidence
        },
        ends: {
          level: result.zoneAnalysis?.ends?.level,
          tone: result.zoneAnalysis?.ends?.tone,
          reflect: result.zoneAnalysis?.ends?.reflect,
          confidence: result.zoneAnalysis?.ends?.confidence
        }
      },
      detectedProcesses: {
        chemicalProcess: result.detectedChemicalProcess,
        lastProcessDate: result.estimatedLastProcessDate,
        homeRemedies: result.detectedHomeRemedies
      },
      riskDetection: result.detectedRisks,
      recommendations: result.recommendations?.slice(0, 3),
      overallConfidence: result.overallConfidence,
      serviceComplexity: result.serviceComplexity,
      estimatedTime: result.estimatedTime,
      tokensUsed: totalTokens,
      costUsd: costUsd.toFixed(4)
    });

    // Save to cache using improved deterministic key generation
    const inputHash = generateCacheKey('diagnose_image', payload);
    await saveToCache(
      salonId,
      'diagnose_image',
      inputHash,
      payload,
      result,
      'gpt-4o',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    // Image diagnosis failed
    return { success: false, error: error.message };
  }
}

async function analyzeDesiredLook(payload: any, salonId: string): Promise<AIResponse> {
  const { imageUrl, imageBase64, currentLevel, diagnosis } = payload;

  // Mantener compatibilidad hacia atrás: si no hay diagnosis, usar currentLevel
  const hairContext = diagnosis || { averageLevel: currentLevel || 6 };

  // Extract current level and state for colorimetry validation
  const actualCurrentLevel =
    diagnosis?.level ||
    diagnosis?.averageLevel ||
    diagnosis?.averageDepthLevel ||
    currentLevel ||
    6;
  const currentState =
    diagnosis?.state?.toLowerCase().includes('teñido') ||
    diagnosis?.state?.toLowerCase().includes('colored')
      ? 'colored'
      : diagnosis?.state?.toLowerCase().includes('decolorado') ||
          diagnosis?.state?.toLowerCase().includes('bleached')
        ? 'bleached'
        : 'natural';

  // Soportar ambos formatos para compatibilidad
  let imageDataUrl: string;

  if (imageUrl) {
    // Convertir URL (privada firmada o pública) a base64 para OpenAI
    // Converting URL (private signed or public) to base64
    imageDataUrl = await urlToBase64(imageUrl);
  } else if (imageBase64) {
    // Mantener compatibilidad con base64
    // Using base64 for backward compatibility

    // Validar que imageBase64 no sea null o undefined
    if (!imageBase64) {
      throw new Error('Image base64 is empty');
    }

    // Validar y limpiar base64
    const cleanBase64 = imageBase64.replace(/\s/g, '').replace(/\n/g, '');

    // Verificar tamaño del base64
    const base64SizeInBytes = (cleanBase64.length * 3) / 4;
    // Base64 size validated for desired look

    // Limitar tamaño máximo
    if (base64SizeInBytes > 4 * 1024 * 1024) {
      throw new Error('Image too large. Maximum size is 4MB');
    }

    // Validar formato
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
      throw new Error('Invalid base64 format');
    }

    imageDataUrl = `data:image/jpeg;base64,${cleanBase64}`;
  } else {
    throw new Error('No image provided');
  }

  // Si tenemos diagnóstico completo, incluir información relevante en el prompt
  const diagnosticContext = hairContext.averageLevel
    ? `
  El análisis del cabello actual muestra:
  - Nivel actual: ${hairContext.averageLevel}
  - Tono actual: ${hairContext.overallTone || 'No especificado'}
  - Estado: ${hairContext.detectedChemicalProcess || 'Natural'}
  - Condición: ${hairContext.overallCondition || 'Buena'}
  ${hairContext.zoneAnalysis?.roots?.grayPercentage ? `- Canas: ${hairContext.zoneAnalysis.roots.grayPercentage}%` : ''}
  
  Considera estos factores al evaluar la viabilidad del color deseado.`
    : '';

  const prompt = `Analiza esta imagen de referencia de color de cabello deseado.
  ${diagnosticContext}
  
  IMPORTANTE - Principios de colorimetría para calcular sesiones:
  - COLOR NO LEVANTA COLOR: Si el cabello tiene color artificial (Teñido/Colored), NO se puede aclarar con tinte
  - Cabello natural: puede aclarar hasta 3 niveles con tinte en una sesión
  - Cabello con color: requiere decapado primero para aclarar cualquier nivel
  - Máximo aclarado por sesión con decoloración: 4 niveles
  - Al oscurecer 3+ niveles: requiere pre-pigmentación
  
  Para calcular estimatedSessions:
  - Si necesita aclarar cabello con color: mínimo 2 sesiones (decapado + decoloración)
  - Si aclara >4 niveles: dividir entre 4 y redondear hacia arriba
  - Si el proceso no es viable: indicar en warnings
  
  Proporciona un análisis en formato JSON con esta estructura exacta:
  {
    "detectedLevel": número decimal del nivel objetivo general,
    "detectedTone": "tono principal detectado",
    "detectedTechnique": "técnica de aplicación detectada",
    "detectedTones": ["lista de tonos presentes"],
    "viabilityScore": 0-100,
    "estimatedSessions": número de sesiones necesarias (basado en principios de colorimetría),
    "requiredProcesses": ["procesos necesarios"],
    "confidence": porcentaje de confianza,
    "warnings": ["advertencias si las hay"],
    "zoneAnalysis": {
      "roots": {
        "level": número decimal del nivel en raíces,
        "tone": "tono detectado en raíces",
        "reflect": "reflejo detectado (Ceniza, Natural, Dorado, etc.)"
      },
      "mids": {
        "level": número decimal del nivel en medios,
        "tone": "tono detectado en medios",
        "reflect": "reflejo detectado"
      },
      "ends": {
        "level": número decimal del nivel en puntas,
        "tone": "tono detectado en puntas",
        "reflect": "reflejo detectado"
      }
    }
  }`;

  try {
    // Preparing OpenAI request for desired look
    // Image data URL validated
    // Image data URL format checked

    if (!imageDataUrl) {
      throw new Error('Image data URL is undefined');
    }

    // Validar que sea data URL después de conversión
    if (!imageDataUrl.startsWith('data:image/')) {
      throw new Error(
        `Invalid image format. Expected data URL after conversion. Got: ${imageDataUrl.substring(0, 30)}`
      );
    }

    // Validar que la imagen base64 no esté vacía
    if (imageDataUrl === 'data:image/jpeg;base64,') {
      // Empty image data for desired look analysis
      throw new Error('La imagen está vacía o corrupta');
    }

    // Log del tamaño real del base64
    const base64Content = imageDataUrl.split(',')[1];
    if (base64Content) {
    // Base64 content validated
    // Base64 format validated
    }

    const messageContent = [
      { type: 'text', text: prompt },
      { type: 'image_url', image_url: { url: imageDataUrl, detail: 'high' } },
    ];

    // Note: Vision models do NOT support functions/function_call
    // Using only response_format for structured output
    const requestBody = {
      model: 'gpt-4o',
      messages: [
        {
          role: 'user',
          content: messageContent,
        },
      ],
      max_tokens: 800,
      temperature: 0,
      top_p: 1,
      seed: 42,
      response_format: { type: 'json_object' },
    };

    const response = await retryWithBackoff(async () => {
      // Create timeout controller (25s to stay under Supabase 30s limit)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.error('OpenAI request timeout after 25 seconds');
        controller.abort();
      }, 25000);

      try {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${openaiApiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal,
        });
        clearTimeout(timeoutId);
        return response;
      } catch (error: any) {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
          throw new Error('OpenAI request timeout after 25 seconds. Please try again.');
        }
        throw error;
      }
    });

    // Check HTTP status first
    if (!response.ok) {
      // Desired look OpenAI API request failed

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      // Error response received

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();
    // OpenAI response received

    if (data.error) {
      // Desired look OpenAI API error
      throw new Error(data.error.message);
    }

    // Log para debugging
    // Raw AI response received
    const result = JSON.parse(data.choices[0].message.content);
    // Desired look analysis parsed

    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-4o', inputTokens, outputTokens);

    // Save to cache using improved deterministic key generation
    const inputHash = generateCacheKey('analyze_desired_look', payload);
    await saveToCache(
      salonId,
      'analyze_desired_look',
      inputHash,
      payload,
      result,
      'gpt-4o',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    // Desired look analysis failed
    return { success: false, error: error.message };
  }
}

/**
 * Fast basic colorimetry validation - checks only critical rules
 */
async function validateBasicColorimetry(
  formula: any,
  diagnosis: any,
  desiredResult: any
): Promise<{
  isValid: boolean;
  violations: Array<{
    type: string;
    severity: 'critical' | 'error' | 'warning';
    message: string;
    suggestion?: string;
    autoFixAvailable?: boolean;
  }>;
  riskLevel: 'low' | 'medium' | 'high';
  confidence: number;
}> {
  const violations: any[] = [];
  const currentLevel = diagnosis?.averageLevel || diagnosis?.level || 5;
  const desiredLevel = desiredResult?.detectedLevel || desiredResult?.targetLevel || currentLevel;
  const levelDifference = desiredLevel - currentLevel;
  const currentState = diagnosis?.state || 'natural';

  // Rule 1: Color cannot lift color (CRITICAL)
  if (levelDifference > 0 && currentState === 'colored') {
    const hasDecolorationStep = formula.steps?.some((step: any) =>
      step.title?.toLowerCase().includes('decoloración') ||
      step.title?.toLowerCase().includes('bleach') ||
      step.ingredients?.some((ing: any) =>
        ing.product?.toLowerCase().includes('decolorante') ||
        ing.product?.toLowerCase().includes('blondor')
      )
    );

    if (!hasDecolorationStep && levelDifference > 2) {
      violations.push({
        type: 'colorimetry',
        severity: 'critical' as const,
        message: `Imposible elevar ${levelDifference} niveles sobre cabello teñido sin decoloración previa`,
        suggestion: 'Agregar paso de decoloración antes de la coloración',
        autoFixAvailable: true,
      });
    }
  }

  // Rule 2: Maximum lift with permanent color (CRITICAL)
  if (levelDifference > 3 && currentState === 'natural') {
    const hasDecolorationStep = formula.steps?.some((step: any) =>
      step.title?.toLowerCase().includes('decoloración') ||
      step.title?.toLowerCase().includes('bleach')
    );

    if (!hasDecolorationStep) {
      violations.push({
        type: 'colorimetry',
        severity: 'critical' as const,
        message: `Tinte permanente no puede elevar más de 3 niveles (intentando ${levelDifference})`,
        suggestion: 'Agregar paso de decoloración previa',
        autoFixAvailable: true,
      });
    }
  }

  const criticalViolations = violations.filter(v => v.severity === 'critical');
  const errorViolations = violations.filter(v => v.severity === 'error');

  const isValid = criticalViolations.length === 0 && errorViolations.length === 0;
  const riskLevel = criticalViolations.length > 0 ? 'high' :
                   errorViolations.length > 0 ? 'medium' : 'low';
  const confidence = isValid ? 0.85 : Math.max(0.3, 0.85 - (violations.length * 0.15));

  return {
    isValid,
    violations,
    riskLevel,
    confidence,
  };
}

/**
 * Convert AI formula response to FormulaValidator format
 */
function convertToValidatorFormat(
  formulationData: any, 
  diagnosis: any, 
  desiredResult: any, 
  brand: string, 
  line: string
): Formula {
  // Map product types from AI response to validator format
  const mapProductType = (type: string): 'color' | 'developer' | 'additive' | 'bleach' | 'toner' => {
    const typeMap: Record<string, 'color' | 'developer' | 'additive' | 'bleach' | 'toner'> = {
      'color': 'color',
      'tinte': 'color',
      'coloración': 'color',
      'developer': 'developer',
      'oxidante': 'developer',
      'oxydant': 'developer',
      'additive': 'additive',
      'aditivo': 'additive',
      'bleach': 'bleach',
      'decolorante': 'bleach',
      'lightener': 'bleach',
      'toner': 'toner',
      'matizador': 'toner'
    };
    return typeMap[type.toLowerCase()] || 'color';
  };

  // Map step types to ProcessType
  const mapStepType = (stepTitle: string, ingredients: any[]): ProcessType => {
    const title = stepTitle.toLowerCase();
    const hasDecolorante = ingredients.some(ing => 
      ing.type === 'bleach' || ing.productName?.toLowerCase().includes('decolorante')
    );
    const hasToner = ingredients.some(ing => 
      ing.type === 'toner' || ing.productName?.toLowerCase().includes('matizador')
    );
    
    if (hasDecolorante || title.includes('decoloración') || title.includes('bleach')) {
      return ProcessType.BLEACHING;
    }
    if (hasToner || title.includes('matiz') || title.includes('ton')) {
      return ProcessType.TONING;
    }
    if (title.includes('decap') || title.includes('remov')) {
      return ProcessType.COLOR_REMOVAL;
    }
    if (title.includes('pre-pigment') || title.includes('prepigment')) {
      return ProcessType.PRE_PIGMENTATION;
    }
    return ProcessType.DIRECT_COLOR;
  };

  // Convert steps
  const steps: FormulaStep[] = formulationData.steps?.map((step: any, index: number) => {
    const ingredients: FormulaIngredient[] = step.mix?.map((product: any) => {
      const amount = parseFloat(product.quantity?.toString().replace(/[^\d.,]/g, '').replace(',', '.')) || 50;
      const unit = product.unit || 'ml';
      const productType = mapProductType(product.type || 'color');
      
      // Extract volume from product name or properties
      let volume: number | undefined;
      if (productType === 'developer') {
        const volumeMatch = product.productName?.match(/(\d+)\s*vol/i) || 
                           product.volume?.toString().match(/(\d+)/);
        if (volumeMatch) {
          volume = parseInt(volumeMatch[1]);
        }
      }
      
      return {
        product: product.productName || `Producto ${index + 1}`,
        shade: product.shade,
        amount: amount,
        unit: unit as 'ml' | 'g' | 'cm' | 'oz',
        type: productType,
        volume: volume
      };
    }) || [];

    return {
      id: `step-${index + 1}`,
      title: step.stepTitle || `Paso ${index + 1}`,
      ingredients: ingredients,
      processingTime: step.processingTime || 30,
      instructions: step.instructions || '',
      type: mapStepType(step.stepTitle || '', ingredients)
    };
  }) || [];

  // Extract levels from diagnosis and desired result
  const currentLevel = extractHairLevel(diagnosis);
  const desiredLevel = extractDesiredLevel(desiredResult);
  
  // Determine current state
  let currentState: 'natural' | 'colored' | 'bleached' | 'mixed' = 'natural';
  if (diagnosis?.currentColor?.colorHistory?.includes('decoloración') || 
      diagnosis?.currentColor?.colorHistory?.includes('bleach')) {
    currentState = 'bleached';
  } else if (diagnosis?.currentColor?.colorHistory?.includes('color') || 
             diagnosis?.currentColor?.colorHistory?.includes('tinte')) {
    currentState = 'colored';
  } else if (diagnosis?.currentColor?.isNatural === false) {
    currentState = 'mixed';
  }

  return {
    steps: steps,
    currentLevel: currentLevel,
    desiredLevel: desiredLevel,
    currentState: currentState,
    brand: brand || 'Generic',
    line: line || '',
    totalTime: formulationData.totalTime || steps.reduce((sum, step) => sum + step.processingTime, 0),
    warnings: formulationData.warnings || [],
    grayPercentage: diagnosis?.grayPercentage || 0
  };
}

/**
 * Extract hair level from diagnosis (1-10 scale)
 */
function extractHairLevel(diagnosis: any): number {
  if (diagnosis?.currentColor?.level) {
    return parseInt(diagnosis.currentColor.level.toString());
  }
  
  // Fallback: try to extract from description
  const colorDesc = diagnosis?.currentColor?.description?.toLowerCase() || '';
  if (colorDesc.includes('muy oscuro') || colorDesc.includes('negro')) return 1;
  if (colorDesc.includes('castaño oscuro')) return 2;
  if (colorDesc.includes('castaño medio')) return 3;
  if (colorDesc.includes('castaño claro')) return 4;
  if (colorDesc.includes('rubio oscuro')) return 5;
  if (colorDesc.includes('rubio medio')) return 6;
  if (colorDesc.includes('rubio claro')) return 7;
  if (colorDesc.includes('rubio muy claro')) return 8;
  if (colorDesc.includes('rubio platino')) return 9;
  if (colorDesc.includes('blanco')) return 10;
  
  return 4; // Default medium brown
}

/**
 * Extract desired level from result
 */
function extractDesiredLevel(desiredResult: any): number {
  if (desiredResult?.desiredColor?.level) {
    return parseInt(desiredResult.desiredColor.level.toString());
  }
  
  // Try to extract from tone/shade
  const shade = desiredResult?.desiredColor?.tone?.toLowerCase() || 
                desiredResult?.desiredColor?.shade?.toLowerCase() || '';
  
  const levelMatch = shade.match(/(\d+)/);
  if (levelMatch) {
    return parseInt(levelMatch[1]);
  }
  
  return 6; // Default medium blonde
}

/**
 * Convert corrected validator formula back to AI format
 */
function convertFromValidatorFormat(correctedFormula: Formula, originalFormulationData: any): any {
  const updated = { ...originalFormulationData };
  
  // Update steps with corrected data
  updated.steps = correctedFormula.steps.map((step, index) => {
    const originalStep = originalFormulationData.steps?.[index] || {};
    
    return {
      ...originalStep,
      stepTitle: step.title,
      processingTime: step.processingTime,
      instructions: step.instructions,
      mix: step.ingredients.map(ingredient => ({
        productName: ingredient.product,
        shade: ingredient.shade,
        quantity: ingredient.amount.toString(),
        unit: ingredient.unit,
        type: ingredient.type,
        volume: ingredient.volume
      }))
    };
  });
  
  // Update total time
  updated.totalTime = correctedFormula.totalTime;
  
  // Merge warnings from corrected formula
  const correctedWarnings = correctedFormula.warnings || [];
  const originalWarnings = originalFormulationData.warnings || [];
  updated.warnings = [...originalWarnings, ...correctedWarnings];
  
  return updated;
}

// =====================================================
// PROVEN FORMULAS INTEGRATION
// =====================================================

/**
 * Generates a deterministic hash for the current scenario
 * This allows us to match identical cases for proven formulas
 */
async function generateScenarioHash(
  diagnosis: any,
  desiredResult: any,
  brand: string,
  line?: string
): Promise<string> {
  // Normalize diagnosis for consistent hashing
  const normalizedDiagnosis = {
    level: diagnosis?.averageDepthLevel || diagnosis?.level || 5,
    state: diagnosis?.zoneAnalysis?.roots?.state || diagnosis?.state || 'natural',
    condition: diagnosis?.overallCondition || 'normal',
    porosity: diagnosis?.porosity || 'normal',
    chemicalProcess: diagnosis?.detectedChemicalProcess || 'none'
  };

  // Normalize desired result
  const normalizedDesired = {
    level: desiredResult?.general?.detectedLevel || desiredResult?.general?.targetLevel || normalizedDiagnosis.level,
    tone: desiredResult?.general?.targetTone || 'neutral',
    technique: desiredResult?.general?.technique || 'full_color',
    coverage: desiredResult?.general?.coverage || 'complete'
  };

  // Create deterministic string for hashing
  const hashInput = JSON.stringify({
    diagnosis: normalizedDiagnosis,
    desired: normalizedDesired,
    brand: brand?.toLowerCase().trim() || '',
    line: line?.toLowerCase().trim() || ''
  });

  // Generate SHA-256 hash using crypto API
  const encoder = new TextEncoder();
  const data = encoder.encode(hashInput);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return hashHex.substring(0, 32);
}

/**
 * Creates a summary for storing in proven formulas
 */
function createDiagnosisSummary(diagnosis: any): string {
  const level = diagnosis?.averageDepthLevel || diagnosis?.level || 5;
  const state = diagnosis?.zoneAnalysis?.roots?.state || diagnosis?.state || 'natural';
  const condition = diagnosis?.overallCondition || 'normal';
  const texture = diagnosis?.texture || 'medium';
  
  return `Level ${level}, ${state.toLowerCase()}, ${texture} texture, ${condition} condition`;
}

/**
 * Creates a summary for desired result
 */
function createDesiredResultSummary(desiredResult: any): string {
  const level = desiredResult?.general?.detectedLevel || desiredResult?.general?.targetLevel || 5;
  const tone = desiredResult?.general?.targetTone || 'neutral';
  const technique = desiredResult?.general?.technique || 'full_color';
  
  return `Target level ${level}, ${tone} tone, ${technique} technique`;
}

/**
 * Searches for proven formulas with exact or similar scenarios
 */
async function findProvenFormula(
  scenarioHash: string,
  diagnosisSummary: string,
  desiredResultSummary: string,
  brand: string,
  salonId: string
): Promise<any | null> {
  try {
    // First, try exact match by scenario hash
    const { data: exactMatch } = await supabase
      .from('proven_formulas')
      .select('*')
      .eq('scenario_hash', scenarioHash)
      .eq('brand', brand)
      .gte('success_count', 3)
      .gte('avg_rating', 4.0)
      .order('avg_rating', { ascending: false })
      .order('success_count', { ascending: false })
      .limit(1)
      .single();

    if (exactMatch) {
      logger.info('Found exact proven formula match:', {
        formulaId: exactMatch.id,
        successCount: exactMatch.success_count,
        avgRating: exactMatch.avg_rating,
        totalUses: exactMatch.total_uses
      });
      return exactMatch;
    }

    // If no exact match, search for similar formulas
    const { data: similarFormulas } = await supabase
      .rpc('find_similar_formulas', {
        p_diagnosis_keywords: diagnosisSummary,
        p_desired_keywords: desiredResultSummary,
        p_brand: brand,
        p_min_rating: 4.0,
        p_limit: 3
      });

    if (similarFormulas && similarFormulas.length > 0) {
      const bestMatch = similarFormulas[0];
      
      // Only use if similarity score is high enough (>0.7)
      if (bestMatch.similarity_score > 0.7) {
        logger.info('Found similar proven formula:', {
          formulaId: bestMatch.id,
          similarityScore: bestMatch.similarity_score,
          successCount: bestMatch.success_count,
          avgRating: bestMatch.avg_rating
        });
        return bestMatch;
      }
    }

    logger.info('No suitable proven formula found, will generate new formula');
    return null;
  } catch (error: any) {
    logger.error('Error searching proven formulas:', {
      error: error.message,
      scenarioHash,
      brand
    });
    return null;
  }
}

/**
 * Saves a new formula as a proven formula
 */
async function saveAsProvenFormula(
  scenarioHash: string,
  diagnosisSummary: string,
  desiredResultSummary: string,
  formula: any,
  brand: string,
  line?: string
): Promise<void> {
  try {
    const { data, error } = await supabase
      .rpc('upsert_proven_formula', {
        p_diagnosis_summary: diagnosisSummary,
        p_desired_result_summary: desiredResultSummary,
        p_formula: formula,
        p_brand: brand,
        p_line: line || null,
        p_initial_rating: 0 // Mark as pending validation
      });

    if (error) {
      throw error;
    }

    logger.info('Formula saved as proven formula:', {
      formulaId: data,
      scenarioHash,
      brand,
      line
    });
  } catch (error: any) {
    logger.error('Error saving proven formula:', {
      error: error.message,
      scenarioHash,
      brand
    });
    // Don't throw - this is not critical for the main flow
  }
}

// El resto del código permanece igual...
async function generateFormula(payload: any, salonId: string): Promise<AIResponse> {
  const { diagnosis, desiredResult, brand, line, clientHistory, regionalConfig, inventoryLevel } =
    payload;

  // =====================================================
  // PROVEN FORMULAS LOOKUP
  // =====================================================

  // Generate scenario hash for proven formula lookup
  const scenarioHash = await generateScenarioHash(diagnosis, desiredResult, brand || '', line);
  const diagnosisSummary = createDiagnosisSummary(diagnosis);
  const desiredResultSummary = createDesiredResultSummary(desiredResult);

  logger.info('Checking for proven formulas:', {
    scenarioHash,
    diagnosisSummary,
    desiredResultSummary,
    brand,
    line
  });

  // Search for existing proven formula
  const provenFormula = await findProvenFormula(
    scenarioHash,
    diagnosisSummary,
    desiredResultSummary,
    brand || '',
    salonId
  );

  // If we found a proven formula, use it directly
  if (provenFormula) {
    logger.info('Using proven formula instead of generating new one:', {
      formulaId: provenFormula.id,
      successCount: provenFormula.success_count,
      avgRating: provenFormula.avg_rating
    });

    // Extract the saved formula data
    const formulationData = provenFormula.formula;
    
    // Add proven formula metadata
    formulationData.provenFormula = {
      id: provenFormula.id,
      successCount: provenFormula.success_count,
      avgRating: provenFormula.avg_rating,
      totalUses: provenFormula.total_uses,
      isProven: true,
      confidence: Math.min(0.95, 0.7 + (provenFormula.avg_rating / 5.0) * 0.25)
    };

    // Add success indicator to warnings
    if (!formulationData.warnings) {
      formulationData.warnings = [];
    }
    formulationData.warnings.unshift(
      `✅ FÓRMULA PROBADA: Esta fórmula ha sido exitosa ${provenFormula.success_count} veces con una calificación promedio de ${provenFormula.avg_rating}/5.0`
    );

    // === BASIC SAFETY VALIDATION FOR PROVEN FORMULAS ===
    try {
      // Run basic validation even on proven formulas for additional safety
      const basicWarnings = await validateWithTimeout(
        formulationData,
        diagnosis,
        brand,
        1000,
        desiredResult
      );
      
      // Add basic warnings if any found
      if (basicWarnings.length > 0) {
        formulationData.warnings.push(...basicWarnings);
        
        logger.info('Basic validation completed on proven formula with warnings:', {
          formulaId: provenFormula.id,
          warningCount: basicWarnings.length,
          warnings: basicWarnings
        });
      } else {
        logger.info('Basic validation passed on proven formula');
      }
    } catch (error) {
      // Fail silently - validation is optional enhancement
      logger.warn('Basic validation failed on proven formula (continuing normally):', error);
    }

    // Generate markdown version
    let formulaText = `# ${formulationData.formulaTitle}\n\n`;
    formulaText += `**✅ FÓRMULA PROBADA** - Éxito comprobado: ${provenFormula.success_count} usos exitosos, calificación ${provenFormula.avg_rating}/5.0\n\n`;
    formulaText += `**Resumen:** ${formulationData.summary}\n\n`;

    if (formulationData.warnings && formulationData.warnings.length > 0) {
      formulaText += `## ⚠️ Advertencias\n`;
      formulationData.warnings.forEach((warning: string) => {
        formulaText += `- ${warning}\n`;
      });
      formulaText += `\n`;
    }

    formulaText += `## Pasos del Proceso\n\n`;
    formulationData.steps.forEach((step: any) => {
      formulaText += `### ${step.stepTitle}\n\n`;

      if (step.mix && step.mix.length > 0) {
        formulaText += `**Mezcla:**\n`;
        step.mix.forEach((product: any) => {
          formulaText += `- ${product.productName}: ${product.quantity}${product.unit}\n`;
        });
        formulaText += `\n`;
      }

      if (step.technique) {
        formulaText += `**Técnica:** ${step.technique.name}\n`;
        formulaText += `${step.technique.description}\n\n`;
      }

      formulaText += `**Instrucciones:** ${step.instructions}\n\n`;

      if (step.processingTime) {
        formulaText += `**Tiempo de procesamiento:** ${step.processingTime} minutos\n\n`;
      }
    });

    formulaText += `\n**Tiempo total estimado:** ${formulationData.totalTime} minutos\n`;

    // =====================================================
    // CRITICAL FIX #3: PARALLEL PROCESSING FOR NON-CRITICAL OPERATIONS
    // =====================================================
    let simpleExplanations: string[] = [];
    let provenMixingSuggestions: string[] = [];
    let enhancedQuickSummary = `✅ Fórmula probada con ${provenFormula.success_count} usos exitosos`;

    // Run explanations and mixing suggestions in parallel using Promise.allSettled
    const [explanationResult, mixingResult] = await Promise.allSettled([
      // Explanation generation with timeout
      formulationData && diagnosis ? Promise.race([
        generateQuickExplanation(formulationData, diagnosis),
        new Promise<string[]>((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), 500)
        )
      ]) : Promise.resolve([]),

      // Mixing suggestions with timeout
      (inventoryLevel && formulationData && formulationData.steps) ? (async () => {
        const extractedTones = extractTonesFromFormula(formulationData);
        if (extractedTones.length > 0) {
          const mockAvailableTones = generateMockInventoryTones(brand, line);
          return await checkForMixingOpportunities(
            extractedTones,
            mockAvailableTones,
            200 // 200ms max timeout for proven formulas
          );
        }
        return [];
      })() : Promise.resolve([])
    ]);

    // Process explanation results
    if (explanationResult.status === 'fulfilled' && explanationResult.value.length > 0) {
      simpleExplanations = explanationResult.value;
      formulationData.explanations = simpleExplanations;
      logger.info('Simple explanations generated for proven formula:', {
        explanationCount: simpleExplanations.length,
        formulaId: provenFormula.id
      });
    } else if (explanationResult.status === 'rejected') {
      logger.warn('Simple explanations failed on proven formula (continuing normally):', {
        error: explanationResult.reason?.message || 'Unknown error',
        formulaId: provenFormula.id
      });
    }

    // Process mixing results
    if (mixingResult.status === 'fulfilled' && mixingResult.value.length > 0) {
      provenMixingSuggestions = mixingResult.value;
      formulationData.mixingSuggestions = provenMixingSuggestions;
      logger.info('Mixing suggestions generated for proven formula:', {
        suggestionCount: provenMixingSuggestions.length,
        formulaId: provenFormula.id
      });
    } else if (mixingResult.status === 'rejected') {
      logger.warn('Mixing suggestions failed on proven formula (continuing normally):', {
        error: mixingResult.reason?.message || 'Unknown error',
        formulaId: provenFormula.id
      });
    }

    return {
      success: true,
      data: {
        formulaText,
        formulationData,
        explanations: simpleExplanations,
        quickSummary: enhancedQuickSummary,
        mixingSuggestions: provenMixingSuggestions,
        totalTokens: 0, // No OpenAI tokens used
        isProvenFormula: true
      },
    };
  }

  // =====================================================
  // CONTINUE WITH AI GENERATION
  // =====================================================

  logger.info('No suitable proven formula found, generating new formula with AI');

  // Determinar configuración regional
  const volumeUnit = regionalConfig?.volumeUnit || 'ml';
  const weightUnit = regionalConfig?.weightUnit || 'g';
  const developerTerm = regionalConfig?.developerTerminology || 'oxidante';
  const colorTerm = regionalConfig?.colorTerminology || 'tinte';
  const maxDeveloperVolume = regionalConfig?.maxDeveloperVolume || 40;
  const currencySymbol = regionalConfig?.currencySymbol || '€';
  const measurementSystem = regionalConfig?.measurementSystem || 'metric';
  const decimalSeparator = regionalConfig?.decimalSeparator || ',';

  // Detectar idioma
  const isEnglish = regionalConfig?.language === 'en';

  // Extraer técnica seleccionada
  const selectedTechnique = desiredResult?.general?.technique || 'full_color';
  const customTechnique = desiredResult?.general?.customTechnique;

  // Obtener instrucciones específicas por técnica
  const getTechniqueInstructions = () => {
    if (selectedTechnique === 'custom' && customTechnique) {
      return isEnglish
        ? `Custom technique described as: "${customTechnique}". Adapt the formula accordingly.`
        : `Técnica personalizada descrita como: "${customTechnique}". Adapta la fórmula según corresponda.`;
    }

    const techniquePrompts = {
      full_color: isEnglish
        ? `- Single formula for complete coverage
- Ensure uniform application from roots to ends
- Consider natural regrowth for maintenance`
        : `- Fórmula única para cobertura completa
- Asegurar aplicación uniforme de raíces a puntas
- Considerar crecimiento natural para mantenimiento`,

      highlights: isEnglish
        ? `- Use foil technique for precision
- Create multiple formulas if needed (base + highlights)
- Thicker consistency to prevent bleeding
- Maximum ${developerTerm} 30 vol for highlights
- Consider placement pattern (full head, partial, face-framing)`
        : `- Usar técnica con papel aluminio para precisión
- Crear múltiples fórmulas si es necesario (base + mechas)
- Consistencia más espesa para evitar sangrado
- Máximo ${developerTerm} 30 vol para mechas
- Considerar patrón de colocación (cabeza completa, parcial, contorno facial)`,

      balayage: isEnglish
        ? `- Free-hand painting technique
- Gradual transition from dark to light
- Use lower ${developerTerm} volume (20 vol max recommended)
- Creamy consistency for controlled application
- Natural, sun-kissed effect
- Consider using clay or cream lightener`
        : `- Técnica de pintado a mano alzada
- Transición gradual de oscuro a claro
- Usar ${developerTerm} de menor volumen (20 vol máximo recomendado)
- Consistencia cremosa para aplicación controlada
- Efecto natural, como besado por el sol
- Considerar usar decolorante en crema o arcilla`,

      ombre: isEnglish
        ? `- Clear horizontal gradient
- Multiple formulas for different zones
- Seamless blending is crucial
- Start application from ends, work up
- Consider toner for perfect transition`
        : `- Degradado horizontal claro
- Múltiples fórmulas para diferentes zonas
- La mezcla perfecta es crucial
- Comenzar aplicación desde puntas, subir gradualmente
- Considerar toner para transición perfecta`,

      babylights: isEnglish
        ? `- Ultra-fine sections (max 1-2mm)
- Low ${developerTerm} volume (10-20 vol)
- Natural, subtle effect
- Longer processing time due to fine sections
- Mimic natural sun-lightened strands`
        : `- Secciones ultrafinas (máx 1-2mm)
- ${developerTerm} de bajo volumen (10-20 vol)
- Efecto natural y sutil
- Mayor tiempo de procesamiento por secciones finas
- Imitar mechones aclarados naturalmente por el sol`,

      color_correction: isEnglish
        ? `- Analyze underlying pigments carefully
- May need pre-pigmentation or color removal
- Multiple steps might be required
- Use appropriate neutralizing tones
- Consider strand test mandatory
- Document each step for future reference`
        : `- Analizar cuidadosamente pigmentos subyacentes
- Puede necesitar pre-pigmentación o remoción de color
- Pueden requerirse múltiples pasos
- Usar tonos neutralizantes apropiados
- Considerar prueba de mechón obligatoria
- Documentar cada paso para referencia futura`,

      foilyage: isEnglish
        ? `- Combine foil and balayage techniques
- Use foil for stronger lift at top sections
- Free-hand painting for natural flow
- Varying ${developerTerm} volumes by section
- Creates maximum dimension`
        : `- Combinar técnicas de aluminio y balayage
- Usar aluminio para mayor aclarado en secciones superiores
- Pintado a mano para flujo natural
- Variar volúmenes de ${developerTerm} por sección
- Crea máxima dimensión`,

      money_piece: isEnglish
        ? `- Focus on face-framing sections
- High contrast for impact
- Protect surrounding hair
- Consider client's skin tone
- Easy maintenance placement`
        : `- Enfoque en secciones que enmarcan el rostro
- Alto contraste para impacto
- Proteger cabello circundante
- Considerar tono de piel del cliente
- Colocación de fácil mantenimiento`,

      chunky_highlights: isEnglish
        ? `- Thick sections (1cm or more)
- Bold contrast recommended
- Strategic placement for maximum effect
- Higher ${developerTerm} volume acceptable (up to 40 vol)
- 90s-inspired dramatic look`
        : `- Secciones gruesas (1cm o más)
- Contraste audaz recomendado
- Colocación estratégica para máximo efecto
- ${developerTerm} de mayor volumen aceptable (hasta 40 vol)
- Look dramático inspirado en los 90s`,

      reverse_balayage: isEnglish
        ? `- Add depth to over-lightened hair
- Use demi-permanent or semi-permanent color
- Focus on roots and mid-lengths
- Create natural shadow root
- Low ${developerTerm} volume or no ${developerTerm}`
        : `- Agregar profundidad a cabello sobre-aclarado
- Usar color demi-permanente o semi-permanente
- Enfoque en raíces y medios
- Crear raíz sombreada natural
- ${developerTerm} de bajo volumen o sin ${developerTerm}`,
    };

    return (
      techniquePrompts[selectedTechnique as keyof typeof techniquePrompts] ||
      techniquePrompts.full_color
    );
  };

  // Crear ejemplos de formato según la región
  const formatExamples =
    measurementSystem === 'metric'
      ? isEnglish
        ? `- Quantities: "40${volumeUnit} of ${colorTerm} 7.1", "60${volumeUnit} of ${developerTerm} 20 vol"
  - Ratios: "1:1${decimalSeparator}5" or "1:2"
  - Weights: "15${weightUnit} of lightening powder"`
        : `- Cantidades: "40${volumeUnit} de ${colorTerm} 7.1", "60${volumeUnit} de ${developerTerm} 20 vol"
  - Proporciones: "1:1${decimalSeparator}5" o "1:2"
  - Pesos: "15${weightUnit} de polvo decolorante"`
      : `- Quantities: "1.35${volumeUnit} of ${colorTerm} 7.1", "2${volumeUnit} of ${developerTerm} 20 vol"
  - Ratios: "1:1.5" or "1:2"
  - Weights: "0.5${weightUnit} of lightening powder"`;

  // Adaptar restricciones según región
  const volumeRestriction =
    maxDeveloperVolume < 40
      ? isEnglish
        ? `IMPORTANT: In this region, the maximum allowed ${developerTerm} volume is ${maxDeveloperVolume} volumes.`
        : `IMPORTANTE: En esta región, el volumen máximo permitido de ${developerTerm} es ${maxDeveloperVolume} volúmenes.`
      : '';

  const techniqueInstructions = getTechniqueInstructions();
  const techniqueName =
    selectedTechnique === 'custom' && customTechnique
      ? customTechnique
      : {
          full_color: isEnglish ? 'Full Color' : 'Tinte Completo',
          highlights: isEnglish ? 'Highlights' : 'Mechas',
          balayage: 'Balayage',
          ombre: 'Ombré',
          babylights: 'Babylights',
          color_correction: isEnglish ? 'Color Correction' : 'Corrección de Color',
          foilyage: 'Foilyage',
          money_piece: 'Money Piece',
          chunky_highlights: isEnglish ? 'Chunky Highlights' : 'Mechas Gruesas',
          reverse_balayage: 'Reverse Balayage',
        }[selectedTechnique] || (isEnglish ? 'Full Color' : 'Tinte Completo');

  // Validate color process according to colorimetry principles
  let colorimetryInstructions = '';
  let colorimetryWarnings: string[] = [];

  try {
    // Extract current and desired levels from diagnosis
    const currentLevel =
      diagnosis?.averageDepthLevel ||
      diagnosis?.level ||
      diagnosis?.zoneAnalysis?.roots?.depth ||
      5; // Default to level 5 if not found

    const desiredLevel =
      desiredResult?.general?.detectedLevel || desiredResult?.general?.targetLevel || currentLevel; // Default to current if not specified

    // Determine current hair state
    const currentState =
      diagnosis?.detectedChemicalProcess?.toLowerCase().includes('color') ||
      diagnosis?.state?.toLowerCase().includes('teñido') ||
      diagnosis?.state?.toLowerCase().includes('colored')
        ? 'colored'
        : diagnosis?.detectedChemicalProcess?.toLowerCase().includes('bleach') ||
            diagnosis?.state?.toLowerCase().includes('decolorado') ||
            diagnosis?.state?.toLowerCase().includes('bleached')
          ? 'bleached'
          : 'natural';

    const colorProcess: ColorProcess = {
      currentLevel: Math.round(currentLevel),
      desiredLevel: Math.round(desiredLevel),
      currentState: currentState as 'natural' | 'colored' | 'bleached',
      hasMetallicSalts: diagnosis?.detectedRisks?.metallic || false,
      hasHenna: diagnosis?.detectedRisks?.henna || false,
    };

    // Validating color process

    const validation = validateColorProcess(colorProcess, maxDeveloperVolume);
    colorimetryInstructions = getColorimetryInstructions(validation, isEnglish ? 'en' : 'es');
    colorimetryWarnings = validation.warnings;

    // Colorimetry validation completed
  } catch (error) {
    // Colorimetry validation failed
    // Continue without colorimetry validation if there's an error
  }

  // JSON structure definition for the prompt
  const jsonStructure = `interface ProductMix {
  productId: string;
  productName: string;
  brand: string;
  line?: string;
  type: string;
  shade?: string;
  quantity: number;
  unit: 'gr' | 'ml' | 'gotas' | 'pulsaciones';
}

interface ApplicationTechnique {
  name: string;
  description: string;
}

interface FormulationStep {
  stepNumber: number;
  stepTitle: string;
  mix?: ProductMix[];
  technique?: ApplicationTechnique;
  instructions: string;
  processingTime?: number;
}

interface Formulation {
  formulaTitle: string;
  summary: string;
  steps: FormulationStep[];
  totalTime: number;
  warnings?: string[];
}`;

  // Get brand-specific expertise
  let brandExpertiseSection = '';
  let brandValidationRules = null;
  let brandExamples = '';

  if (brand) {
    try {
      // Loading brand expertise

      // Get comprehensive brand expertise
      const brandExpertise = getBrandExpertise(brand, line || '', isEnglish ? 'en' : 'es');

      // Get validation rules for the brand
      brandValidationRules = getBrandValidationRules(brand);

      // Get example formulas for the brand and technique
      brandExamples = getBrandExampleFormulas(
        brand,
        line || '',
        selectedTechnique,
        isEnglish ? 'en' : 'es'
      );

      // Build the brand expertise section for the prompt
      if (isEnglish) {
        brandExpertiseSection = `
**BRAND EXPERTISE - ${brand.toUpperCase()} ${line ? line.toUpperCase() : ''} SPECIALIST:**

${brandExpertise.personality}

**Technical Knowledge:**
${brandExpertise.technicalKnowledge}

**Brand-Specific Rules:**
${brandExpertise.specificRules}

**Mixing Ratios:**
${brandExpertise.mixingRatios}

**Special Products & Additives:**
${brandExpertise.specialProducts}

**Processing Times:**
${brandExpertise.processingTimes}

**Professional Tips:**
${brandExpertise.proTips}

**Nomenclature System:**
${brandExpertise.nomenclature}

${brandExamples ? `**Reference Formulas:**\n${brandExamples}` : ''}

IMPORTANT: As a ${brand} expert, you MUST follow their specific standards and recommendations exactly.
`;
      } else {
        brandExpertiseSection = `
**EXPERTISE DE MARCA - ESPECIALISTA ${brand.toUpperCase()} ${line ? line.toUpperCase() : ''}:**

${brandExpertise.personality}

**Conocimiento Técnico:**
${brandExpertise.technicalKnowledge}

**Reglas Específicas de la Marca:**
${brandExpertise.specificRules}

**Proporciones de Mezcla:**
${brandExpertise.mixingRatios}

**Productos Especiales y Aditivos:**
${brandExpertise.specialProducts}

**Tiempos de Procesamiento:**
${brandExpertise.processingTimes}

**Tips Profesionales:**
${brandExpertise.proTips}

**Sistema de Nomenclatura:**
${brandExpertise.nomenclature}

${brandExamples ? `**Fórmulas de Referencia:**\n${brandExamples}` : ''}

IMPORTANTE: Como experto en ${brand}, DEBES seguir exactamente sus estándares y recomendaciones específicas.
`;
      }

    // Brand expertise loaded
    } catch (error) {
      // Brand expertise loading failed
      // Continue without brand expertise if there's an error
    }
  }

  // Get inventory products if not in 'solo-formulas' mode
  let inventoryContext = '';
  if (inventoryLevel && inventoryLevel !== 'solo-formulas' && salonId) {
    try {
    // Loading inventory products

      // Get products from the salon's inventory
      const { data: products, error } = await supabase
        .from('products')
        .select('brand, line, type, shade, display_name, stock_ml')
        .eq('salon_id', salonId)
        .eq('is_active', true)
        .order('brand', { ascending: true })
        .order('shade', { ascending: true });

      if (!error && products && products.length > 0) {
    // Products loaded from inventory

        // Group products by type for better organization
        const productsByType = products.reduce((acc: any, product) => {
          const type = product.type || 'otro';
          if (!acc[type]) acc[type] = [];

          // Create a simplified product description
          const productDesc =
            product.display_name ||
            `${product.brand} ${product.line || ''} ${product.shade || ''}`.trim();

          acc[type].push(productDesc);
          return acc;
        }, {});

        // Build inventory context with strong directives
        const inventoryLines = [];

        if (isEnglish) {
          inventoryLines.push('\n**CRITICAL INVENTORY INSTRUCTIONS:**');
          inventoryLines.push(
            '1. You MUST use the EXACT product names below, including specific shade/tone numbers'
          );
          inventoryLines.push(
            '2. CORRECT example: "Wella Illumina Color 7.81" NOT "Illumina Color"'
          );
          inventoryLines.push(
            '3. For each product in your formula, include: brand, line, shade/volume, and quantity'
          );
          inventoryLines.push('');
          inventoryLines.push('**AVAILABLE PRODUCTS (USE THESE EXACT NAMES):**');
        } else {
          inventoryLines.push('\n**INSTRUCCIONES CRÍTICAS DE INVENTARIO:**');
          inventoryLines.push(
            '1. DEBES usar los nombres EXACTOS de productos abajo, incluyendo números específicos de tono/matiz'
          );
          inventoryLines.push(
            '2. Ejemplo CORRECTO: "Wella Illumina Color 7.81" NO "Illumina Color"'
          );
          inventoryLines.push(
            '3. Para cada producto en tu fórmula, incluye: marca, línea, tono/volumen y cantidad'
          );
          inventoryLines.push('');
          inventoryLines.push('**PRODUCTOS DISPONIBLES (USA ESTOS NOMBRES EXACTOS):**');
        }

        // Add products by type with emphasis on exact naming
        Object.entries(productsByType).forEach(([type, productList]: [string, any]) => {
          const typeLabel =
            {
              color: isEnglish ? 'Hair Colors (WITH EXACT SHADE)' : 'Tintes (CON TONO EXACTO)',
              developer: isEnglish
                ? 'Developers (WITH EXACT VOLUME)'
                : 'Oxidantes (CON VOLUMEN EXACTO)',
              bleach: isEnglish ? 'Bleaching Products' : 'Decolorantes',
              treatment: isEnglish ? 'Treatments' : 'Tratamientos',
              toner: isEnglish ? 'Toners' : 'Matizadores',
              additive: isEnglish ? 'Additives' : 'Aditivos',
              pre_pigment: isEnglish ? 'Pre-pigmentation' : 'Pre-pigmentación',
              other: isEnglish ? 'Other' : 'Otros',
            }[type] || type;

          inventoryLines.push(`\n${typeLabel}:`);
          // List each product on its own line for clarity
          productList.forEach((product: string) => {
            inventoryLines.push(`  • ${product}`);
          });
        });

        inventoryLines.push('');

        // Add brand-specific inventory guidance if brand is available
        if (brand) {
          if (isEnglish) {
            inventoryLines.push(`**${brand.toUpperCase()} INVENTORY INTEGRATION:**`);
            inventoryLines.push(
              `- As a ${brand} expert, select the most appropriate products from the available inventory`
            );
            inventoryLines.push(`- Apply ${brand}'s specific mixing ratios and recommendations`);
            inventoryLines.push(
              `- If a recommended ${brand} product is not available, suggest the closest alternative and mark it clearly`
            );
          } else {
            inventoryLines.push(`**INTEGRACIÓN DE INVENTARIO ${brand.toUpperCase()}:**`);
            inventoryLines.push(
              `- Como experto en ${brand}, selecciona los productos más apropiados del inventario disponible`
            );
            inventoryLines.push(
              `- Aplica las proporciones de mezcla y recomendaciones específicas de ${brand}`
            );
            inventoryLines.push(
              `- Si un producto recomendado de ${brand} no está disponible, sugiere la alternativa más cercana y márcalo claramente`
            );
          }
          inventoryLines.push('');
        }

        inventoryLines.push(
          isEnglish
            ? 'IMPORTANT: Products NOT listed above must be marked as "(Not in stock)" in your formula.'
            : 'IMPORTANTE: Los productos NO listados arriba deben marcarse como "(No en stock)" en tu fórmula.'
        );

        inventoryContext = inventoryLines.join('\n');
        logger.debug(
          `Inventory context prepared with ${Object.keys(productsByType).length} product types`
        );
      } else {
    // No products found in inventory
      }
    } catch (error) {
      // Inventory products loading failed
      // Continue without inventory context
    }
  }

  // Create structured prompt that requests JSON output
  const prompt = isEnglish
    ? `You are "Salonier Assistant", a world-renowned Master Colorist expert, specialized in creating precise and safe color formulas.

**MISSION:**
Generate a detailed professional coloring formula based on the client's diagnosis, desired color, and salon preferences.

**GOLDEN RULES:**
1. **MANDATORY OUTPUT FORMAT:** Your response MUST be ONLY a valid JSON object. No introductory text, no markdown, no explanations. IMPORTANT: Do NOT wrap the JSON in code blocks (\`\`\`json) or any markdown formatting. The response must start directly with { and end with }. The JSON must strictly comply with this TypeScript interface:
   \`\`\`typescript
   ${jsonStructure}
   \`\`\`
2. **EXPERT THINKING:** Consider underlying pigments. Adapt formula to ${brand} ${line}. Be explicit about mixing ratios.
3. **SAFETY FIRST:** Add clear warnings if risky.
4. **PROFESSIONAL LANGUAGE:** Use colorist terminology.
5. **PRODUCT SPECIFICITY (CRITICAL):** You MUST be specific with EVERY product:
   - ALWAYS include the exact shade/tone number for colors (e.g., "7.81", "9.60")
   - ALWAYS include the exact volume for developers (e.g., "20 vol", "30 vol")
   - NEVER use generic names like "Illumina Color" without a shade
   - Each product in ProductMix MUST include ALL these fields:
     * brand: Always use "${brand}"
     * line: Use "${line}" if specified
     * type: "Tinte", "Oxidante", "Decolorante", "Tratamiento", etc.
     * shade: The SPECIFIC shade/tone/volume number (MANDATORY)
     * productName: Full product name including shade (e.g., "Wella Illumina Color 7.81")
6. **COLORIMETRY PRINCIPLES (MANDATORY):**
   - COLOR CANNOT LIFT COLOR: If hair has artificial color, you MUST include color removal before lightening
   - DEVELOPER VOLUME RULES:
     * Darkening only: Use 10 volume developer or demi-permanent activator
     * Gray coverage/same level: Use 20 volume
     * Lifting 1-2 levels: Use 20-30 volume
     * Lifting 3+ levels: Requires bleaching, not color
   - PRE-PIGMENTATION: Required when going 3+ levels darker to avoid green/muddy results
   - If the process is not technically viable, include the necessary preparatory steps

**CONTEXT:**
* **Client Diagnosis:** ${JSON.stringify(diagnosis)}
* **Desired Color:** ${JSON.stringify(desiredResult)}
* **Products:** ${brand} ${line}
* **Regional Config:** ${measurementSystem} system, ${volumeUnit}/${weightUnit} units, "${developerTerm}" for developer, "${colorTerm}" for color
* **Technique Requirements:** ${techniqueInstructions}
${colorimetryInstructions}
${brandExpertiseSection}
${inventoryContext}

Generate the JSON formula now.`
    : `Eres "Salonier Assistant", un Maestro Colorista experto de renombre mundial, especializado en crear fórmulas de coloración precisas y seguras.

**MISIÓN:**
Generar una fórmula de coloración profesional detallada basada en el diagnóstico del cliente, color deseado y preferencias del salón.

**REGLAS DE ORO:**
1. **FORMATO OBLIGATORIO:** Tu respuesta DEBE ser SOLO un objeto JSON válido. Sin texto introductorio, sin markdown, sin explicaciones. IMPORTANTE: NO envuelvas el JSON en bloques de código (\`\`\`json) ni ningún formato markdown. La respuesta debe comenzar directamente con { y terminar con }. El JSON debe cumplir estrictamente con esta interfaz TypeScript:
   \`\`\`typescript
   ${jsonStructure}
   \`\`\`
2. **PENSAMIENTO EXPERTO:** Considera pigmentos subyacentes. Adapta fórmula a ${brand} ${line}. Sé explícito con proporciones.
3. **SEGURIDAD PRIMERO:** Añade advertencias claras si hay riesgo.
4. **LENGUAJE PROFESIONAL:** Usa terminología de colorista.
5. **ESPECIFICIDAD DE PRODUCTOS (CRÍTICO):** DEBES ser específico con CADA producto:
   - SIEMPRE incluye el número exacto de tono/matiz para tintes (ej: "7.81", "9.60")
   - SIEMPRE incluye el volumen exacto para oxidantes (ej: "20 vol", "30 vol")
   - NUNCA uses nombres genéricos como "Illumina Color" sin un tono
   - Cada producto en ProductMix DEBE incluir TODOS estos campos:
     * brand: Siempre usa "${brand}"
     * line: Usa "${line}" si está especificada
     * type: "Tinte", "Oxidante", "Decolorante", "Tratamiento", etc.
     * shade: El número ESPECÍFICO de tono/matiz/volumen (OBLIGATORIO)
     * productName: Nombre completo del producto incluyendo tono (ej: "Wella Illumina Color 7.81")
6. **PRINCIPIOS DE COLORIMETRÍA (OBLIGATORIO):**
   - COLOR NO LEVANTA COLOR: Si el cabello tiene color artificial, DEBES incluir decapado antes de aclarar
   - REGLAS DE VOLUMEN DE OXIDANTE:
     * Solo oscurecer: Usa oxidante 10 volúmenes o activador demipermanente
     * Cobertura canas/mismo nivel: Usa 20 volúmenes
     * Aclarar 1-2 niveles: Usa 20-30 volúmenes
     * Aclarar 3+ niveles: Requiere decoloración, no tinte
   - PRE-PIGMENTACIÓN: Requerida al oscurecer 3+ niveles para evitar resultados verdes/cenizos
   - Si el proceso no es técnicamente viable, incluye los pasos preparatorios necesarios

**CONTEXTO:**
* **Diagnóstico Cliente:** ${JSON.stringify(diagnosis)}
* **Color Deseado:** ${JSON.stringify(desiredResult)}
* **Productos:** ${brand} ${line}
* **Config Regional:** Sistema ${measurementSystem}, unidades ${volumeUnit}/${weightUnit}, "${developerTerm}" para oxidante, "${colorTerm}" para tinte
* **Requisitos Técnica:** ${techniqueInstructions}
${colorimetryInstructions}
${brandExpertiseSection}
${inventoryContext}

Genera el JSON de la fórmula ahora.`;

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content:
                regionalConfig?.language === 'en'
                  ? `You are an expert colorist with 20 years of experience specializing in ${techniqueName}. ${brand ? `You are also a certified ${brand} ${line || ''} Technical Expert with deep knowledge of their specific products, formulation rules, and application techniques.` : ''} Always respond in English and use the terminology and units specified in the prompt. You understand the nuances of different application techniques and adapt formulas accordingly. Consider factors like hair porosity, elasticity, and previous chemical processes when creating formulas. ${brand ? `As a ${brand} specialist, you must follow their specific mixing ratios, processing times, and product recommendations exactly.` : ''}`
                  : `Eres un experto colorista con 20 años de experiencia especializado en ${techniqueName}. ${brand ? `También eres un Experto Técnico certificado en ${brand} ${line || ''} con conocimiento profundo de sus productos específicos, reglas de formulación y técnicas de aplicación.` : ''} Siempre responde en español y usa la terminología y unidades especificadas en el prompt. Comprendes los matices de diferentes técnicas de aplicación y adaptas las fórmulas según corresponda. Consideras factores como porosidad, elasticidad y procesos químicos previos al crear fórmulas. ${brand ? `Como especialista en ${brand}, debes seguir exactamente sus proporciones de mezcla específicas, tiempos de procesamiento y recomendaciones de productos.` : ''}`,
            },
            { role: 'user', content: prompt },
          ],
          max_tokens: 2000,
          temperature: 0,
          top_p: 1,
          seed: 42,
        }),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      // Formula generation OpenAI API request failed

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      // Error response received

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();
    // OpenAI response received

    if (data.error) {
      throw new Error(data.error.message);
    }

    const aiResponse = data.choices[0].message.content;
    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-4o', inputTokens, outputTokens);

    let formulationData = null;
    let formulaText = '';

    // Try to parse as JSON with robust extraction
    try {
      // Log first 200 chars of response for debugging
    // AI response preview available

      // Extract clean JSON from potentially markdown-wrapped response
      const cleanJsonString = extractJsonFromString(aiResponse);
      formulationData = JSON.parse(cleanJsonString);
    // Formula parsed as JSON

      // Validate and fix product types in the formula
      if (formulationData && formulationData.steps) {
        formulationData.steps.forEach((step: any) => {
          if (step.mix && Array.isArray(step.mix)) {
            step.mix.forEach((product: any) => {
              if (product.type) {
                const validType = mapCategoryToType(product.type);
                if (product.type !== validType) {
                  // Product type mapped
                  product.type = validType;
                }
              }
            });
          }
        });
      }

      // Add colorimetry warnings if not already included
      if (formulationData && colorimetryWarnings.length > 0) {
        if (!formulationData.warnings) {
          formulationData.warnings = [];
        }
        // Add colorimetry warnings at the beginning
        formulationData.warnings = [...colorimetryWarnings, ...formulationData.warnings];
      }

      // === BASIC SAFETY VALIDATION (FAIL-SAFE) ===
      if (formulationData) {
        try {
          // Run basic validation with strict 1-second timeout
          const basicWarnings = await validateWithTimeout(
            formulationData,
            diagnosis,
            brand,
            1000,
            desiredResult
          );
          
          // Add basic warnings if any found
          if (basicWarnings.length > 0) {
            if (!formulationData.warnings) {
              formulationData.warnings = [];
            }
            // Add basic validation warnings
            formulationData.warnings = [...formulationData.warnings, ...basicWarnings];
            
            logger.info('Basic validation completed with warnings:', {
              warningCount: basicWarnings.length,
              warnings: basicWarnings
            });
          } else {
            logger.info('Basic validation passed - no warnings');
          }
        } catch (error) {
          // Fail silently - validation is optional enhancement
          logger.warn('Basic validation failed (continuing normally):', error);
        }
      }

      // === FORMULA VALIDATION & AUTO-CORRECTION ===
      let validationResult: ValidationResult | null = null;
      let validationWarnings: string[] = [];
      let autoCorrections: string[] = [];
      let validationStatus: 'passed' | 'corrected' | 'failed' = 'passed';

      if (formulationData && formulationData.steps) {
        try {
          // Convert AI response to validator format
          const validatorFormula = convertToValidatorFormat(
            formulationData, 
            diagnosis, 
            desiredResult, 
            brand || 'Generic', 
            line || ''
          );

          // BASIC COLORIMETRY VALIDATION (Fast & Essential)
          const validationResult = await validateBasicColorimetry(
            validatorFormula,
            diagnosis,
            desiredResult
          );
          
          logger.info('Formula validation completed:', {
            isValid: validationResult.isValid,
            violationCount: validationResult.violations.length,
            riskLevel: validationResult.riskLevel,
            confidence: validationResult.confidence
          });

          // Process validation results
          if (!validationResult.isValid) {
            validationStatus = 'failed';
            
            // Extract warnings from violations
            validationWarnings = validationResult.violations.map(violation => {
              const severity = violation.severity === 'critical' ? '🚨 CRÍTICO' : 
                              violation.severity === 'error' ? '⚠️ ERROR' : '💡 SUGERENCIA';
              return `${severity}: ${violation.message}${violation.suggestion ? ` - ${violation.suggestion}` : ''}`;
            });

            // Apply auto-corrections if available
            const correctedFormula = autoCorrectFormula(validatorFormula);
            if (correctedFormula !== validatorFormula) {
              validationStatus = 'corrected';
              
              // Convert corrected formula back to AI format
              formulationData = convertFromValidatorFormat(correctedFormula, formulationData);
              
              // Track corrections made
              const correctionTypes = validationResult.violations
                .filter(v => v.autoFixAvailable)
                .map(v => v.type);
              
              if (correctionTypes.includes('colorimetry')) {
                autoCorrections.push('Se ajustaron los volúmenes de oxidante según principios de colorimetría');
              }
              if (correctionTypes.includes('brand')) {
                autoCorrections.push(`Se corrigieron las proporciones para cumplir estándares de ${brand || 'la marca'}`);
              }
              if (correctionTypes.includes('safety')) {
                autoCorrections.push('Se aplicaron medidas de seguridad adicionales');
              }
              
              logger.info('Formula auto-corrected:', {
                correctionsApplied: autoCorrections.length,
                finalRiskLevel: validationResult.riskLevel
              });
            }
          }

          // Add validation warnings to formula warnings
          if (validationWarnings.length > 0) {
            if (!formulationData.warnings) {
              formulationData.warnings = [];
            }
            formulationData.warnings = [...formulationData.warnings, ...validationWarnings];
          }

          // Add validation metadata to response
          formulationData.validation = {
            status: validationStatus,
            riskLevel: validationResult.riskLevel,
            confidence: validationResult.confidence,
            autoCorrections: autoCorrections,
            validatedAt: new Date().toISOString()
          };

        } catch (validationError: any) {
          logger.error('Formula validation failed:', {
            error: validationError.message,
            stack: validationError.stack
          });
          
          // Add fallback warning
          validationWarnings.push('⚠️ No se pudo validar automáticamente la fórmula. Revise cuidadosamente antes de aplicar.');
          if (!formulationData.warnings) {
            formulationData.warnings = [];
          }
          formulationData.warnings.push(...validationWarnings);
        }
      }

      // Generate markdown version for backward compatibility
      formulaText = `# ${formulationData.formulaTitle}\n\n`;
      formulaText += `**Resumen:** ${formulationData.summary}\n\n`;

      if (formulationData.warnings && formulationData.warnings.length > 0) {
        formulaText += `## ⚠️ Advertencias\n`;
        formulationData.warnings.forEach((warning: string) => {
          formulaText += `- ${warning}\n`;
        });
        formulaText += `\n`;
      }

      formulaText += `## Pasos del Proceso\n\n`;
      formulationData.steps.forEach((step: any) => {
        formulaText += `### ${step.stepTitle}\n\n`;

        if (step.mix && step.mix.length > 0) {
          formulaText += `**Mezcla:**\n`;
          step.mix.forEach((product: any) => {
            formulaText += `- ${product.productName}: ${product.quantity}${product.unit}\n`;
          });
          formulaText += `\n`;
        }

        if (step.technique) {
          formulaText += `**Técnica:** ${step.technique.name}\n`;
          formulaText += `${step.technique.description}\n\n`;
        }

        formulaText += `**Instrucciones:** ${step.instructions}\n\n`;

        if (step.processingTime) {
          formulaText += `**Tiempo de procesamiento:** ${step.processingTime} minutos\n\n`;
        }
      });

      formulaText += `\n**Tiempo total estimado:** ${formulationData.totalTime} minutos\n`;
    } catch (parseError: any) {
      logger.error('Failed to parse formula as JSON, falling back to markdown:', {
        error: parseError.message,
        errorType: parseError.constructor.name,
        aiResponseLength: aiResponse.length,
        aiResponsePrefix: aiResponse.substring(0, 100),
        aiResponseSuffix: aiResponse.substring(Math.max(0, aiResponse.length - 100)),
        hasMarkdownBlocks: aiResponse.includes('```'),
        hasJsonKeyword: aiResponse.includes('json'),
      });

      // Fallback: treat as markdown text
      formulaText = aiResponse;
      // Try to extract some basic structure for compatibility
      formulationData = {
        formulaTitle: techniqueName,
        summary: 'Fórmula generada por IA (formato markdown)',
        steps: [],
        totalTime: 60,
        warnings: [
          'Esta fórmula fue generada en formato markdown debido a problemas de parseo JSON',
        ],
      };
    }

    // Generate formula explanation
    let formulaExplanation = null;
    let quickSummary = '';
    
    try {
      // Create process validation for colorimetry analysis
      const colorProcess: ColorProcess = {
        currentLevel: diagnosis.averageDepthLevel || 5,
        desiredLevel: desiredResult.detectedLevel || diagnosis.averageDepthLevel || 5,
        currentState: diagnosis.zoneAnalysis?.roots?.state === 'Natural' ? 'natural' : 
                     diagnosis.zoneAnalysis?.roots?.state === 'Colored' || diagnosis.zoneAnalysis?.roots?.state === 'Teñido' ? 'colored' :
                     diagnosis.zoneAnalysis?.roots?.state === 'Bleached' || diagnosis.zoneAnalysis?.roots?.state === 'Decolorado' ? 'bleached' : 'natural',
        hasMetallicSalts: diagnosis.detectedRisks?.metallic || false,
        hasHenna: diagnosis.detectedRisks?.henna || false,
      };

      const processValidation = validateColorProcess(colorProcess, regionalConfig?.maxDeveloperVolume || 40);
      
      // Create formula analysis for explainer
      const formulaAnalysis = {
        diagnosis,
        desiredResult,
        formula: formulationData,
        selectedProducts: [], // TODO: Extract from formulationData if available
        processValidation,
        brand: brand || 'Generic',
        line: line || 'Professional',
        technique: selectedTechnique || 'full_color'
      };

      // Initialize explainer with correct language
      // FORMULA EXPLANATIONS TEMPORARILY DISABLED
      // const explainer = new FormulaExplainer(regionalConfig?.language || 'es');
      
      // Generate comprehensive explanation - DISABLED
      // formulaExplanation = explainer.generateExplanation(formulaAnalysis);
      
      // Generate quick summary - DISABLED
      // quickSummary = generateQuickExplanation(
      //   colorProcess.currentLevel,
      //   colorProcess.desiredLevel,
      //   processValidation.recommendedDeveloperVolume,
      //   regionalConfig?.language || 'es'
      // );
      
      // FALLBACK SIMPLE EXPLANATION
      quickSummary = `Proceso de coloración desde nivel ${colorProcess.currentLevel} a nivel ${colorProcess.desiredLevel}`;
      formulaExplanation = {
        confidenceScore: 0.85,
        overallStrategy: 'Coloración profesional estándar',
        levelExplanation: `Cambio de nivel: ${colorProcess.currentLevel} → ${colorProcess.desiredLevel}`,
        developerExplanation: `Volumen recomendado: ${processValidation.recommendedDeveloperVolume}`,
        productChoiceExplanation: 'Productos seleccionados según disponibilidad',
        timingExplanation: 'Tiempos según protocolo estándar',
        processSteps: [],
        riskFactors: [],
        successTips: ['Realizar prueba de mecha', 'Seguir tiempos indicados'],
        colorimetryReasoning: 'Análisis colorimétrico básico aplicado'
      };

    } catch (explanationError: any) {
      // If explanation generation fails, continue without it but log the error
      logger.error('Failed to generate formula explanation:', {
        error: explanationError.message,
        diagnosis: diagnosis?.averageDepthLevel,
        desiredResult: desiredResult?.detectedLevel,
      });
      
      // Provide fallback explanation
      quickSummary = regionalConfig?.language === 'en' 
        ? '💡 Professional formula generated with AI analysis'
        : '💡 Fórmula profesional generada con análisis de IA';
    }

    // =====================================================
    // SAVE NEW FORMULA AS PROVEN FORMULA
    // =====================================================
    // Save this new formula to the proven formulas system for future use
    if (formulationData && formulationData.steps) {
      await saveAsProvenFormula(
        scenarioHash,
        diagnosisSummary,
        desiredResultSummary,
        formulationData,
        brand || 'Generic',
        line
      );

      // Add metadata to indicate this is a new formula pending validation
      formulationData.provenFormula = {
        isProven: false,
        isPending: true,
        confidence: 0.75, // Base confidence for new AI formulas
        needsValidation: true
      };

      // Add pending validation notice
      if (!formulationData.warnings) {
        formulationData.warnings = [];
      }
      formulationData.warnings.push(
        'Fórmula calculada según diagnóstico: Revisa los pasos y ajusta según tu criterio profesional.'
      );
    }

    // =====================================================
    // CRITICAL FIX #3: PARALLEL PROCESSING FOR EXPLANATIONS & MIXING
    // =====================================================
    let simpleExplanations: string[] = [];
    let mixingSuggestions: string[] = [];
    let enhancedQuickSummary = quickSummary;

    // Run explanations and mixing suggestions in parallel using Promise.allSettled
    const [explanationResult, mixingResult] = await Promise.allSettled([
      // Explanation generation with timeout
      formulationData && diagnosis ? Promise.race([
        generateQuickExplanation(formulationData, diagnosis),
        new Promise<string[]>((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), 500)
        )
      ]) : Promise.resolve([]),

      // Mixing suggestions with timeout
      (inventoryLevel && formulationData && formulationData.steps) ? (async () => {
        const extractedTones = extractTonesFromFormula(formulationData);
        if (extractedTones.length > 0) {
          const mockAvailableTones = generateMockInventoryTones(brand, line);
          return await checkForMixingOpportunities(
            extractedTones,
            mockAvailableTones,
            300 // 300ms max timeout
          );
        }
        return [];
      })() : Promise.resolve([])
    ]);

    // Process explanation results
    if (explanationResult.status === 'fulfilled' && explanationResult.value.length > 0) {
      simpleExplanations = explanationResult.value;

      // Enhance quick summary with first explanation
      if (simpleExplanations.length > 0) {
        enhancedQuickSummary = simpleExplanations[0];
      }

      // Add explanations to formula data
      formulationData.explanations = simpleExplanations;
      formulationData.quickSummary = enhancedQuickSummary;

      logger.info('Simple explanations generated:', {
        explanationCount: simpleExplanations.length,
        quickSummary: enhancedQuickSummary
      });
    } else if (explanationResult.status === 'rejected') {
      logger.warn('Simple explanations failed (continuing normally):', {
        error: explanationResult.reason?.message || 'Unknown error',
        hasFormulationData: !!formulationData,
        hasDiagnosis: !!diagnosis
      });
    }

    // Process mixing results
    if (mixingResult.status === 'fulfilled' && mixingResult.value.length > 0) {
      mixingSuggestions = mixingResult.value;
      formulationData.mixingSuggestions = mixingSuggestions;

      logger.info('Mixing suggestions generated:', {
        suggestionCount: mixingSuggestions.length
      });
    } else if (mixingResult.status === 'rejected') {
      logger.warn('Mixing suggestions failed (continuing normally):', {
        error: mixingResult.reason?.message || 'Unknown error',
        hasInventoryLevel: !!inventoryLevel,
        hasFormulationData: !!formulationData
      });
    }

    // Save to cache (now including explanation)
    const inputHash = generateCacheKey('generate_formula', {
      diagnosis,
      desiredResult,
      brand,
      line,
    });
    await saveToCache(
      salonId,
      'generate_formula',
      inputHash,
      payload,
      { 
        formulaText, 
        formulationData, 
        explanation: formulaExplanation,
        quickSummary: enhancedQuickSummary,
        explanations: simpleExplanations
      },
      'gpt-4o',
      totalTokens,
      costUsd
    );

    return {
      success: true,
      data: {
        formulaText,
        formulationData,
        explanation: formulaExplanation,
        explanations: simpleExplanations,
        quickSummary: enhancedQuickSummary,
        mixingSuggestions,
        totalTokens,
        isProvenFormula: false,
        scenarioHash
      },
    };
  } catch (error: any) {
    // Formula generation failed
    return { success: false, error: error.message };
  }
}

async function convertFormula(payload: any, salonId: string): Promise<AIResponse> {
  const { originalBrand, originalLine, originalFormula, targetBrand, targetLine, regionalConfig } =
    payload;

  // Detectar idioma y configuración regional
  const isEnglish = regionalConfig?.language === 'en';
  const volumeUnit = regionalConfig?.volumeUnit || 'ml';
  const weightUnit = regionalConfig?.weightUnit || 'g';
  const developerTerm = regionalConfig?.developerTerminology || 'oxidante';
  const colorTerm = regionalConfig?.colorTerminology || 'tinte';

  const prompt = isEnglish
    ? `Convert this hair color formula:
  
  Original brand: ${originalBrand} - ${originalLine}
  Formula: ${originalFormula}
  
  To target brand: ${targetBrand} - ${targetLine}
  
  Provide:
  1. Converted formula with equivalent products
  2. Necessary adjustments in ratios or timing
  3. Warnings about differences between brands
  4. Confidence level in the conversion
  
  Use ${volumeUnit} for volumes, ${weightUnit} for weights, "${developerTerm}" for developer, and "${colorTerm}" for color.`
    : `Convierte esta fórmula de coloración:
  
  Marca original: ${originalBrand} - ${originalLine}
  Fórmula: ${originalFormula}
  
  A la marca objetivo: ${targetBrand} - ${targetLine}
  
  Proporciona:
  1. Fórmula convertida con productos equivalentes
  2. Ajustes necesarios en proporciones o tiempos
  3. Advertencias sobre diferencias entre marcas
  4. Nivel de confianza en la conversión
  
  Usa ${volumeUnit} para volúmenes, ${weightUnit} para pesos, "${developerTerm}" para oxidante, y "${colorTerm}" para coloración.`;

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: [
            {
              role: 'system',
              content: isEnglish
                ? 'You are an expert colorist specialized in converting formulas between different hair color brands. Always respond in English.'
                : 'Eres un experto colorista especializado en convertir fórmulas entre diferentes marcas de coloración. Siempre responde en español.',
            },
            { role: 'user', content: prompt },
          ],
          max_tokens: 1000,
          temperature: 0,
          top_p: 1,
          seed: 42,
        }),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      // Formula conversion OpenAI API request failed

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      // Error response received

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error.message);
    }

    const result = data.choices[0].message.content;
    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-4o-mini', inputTokens, outputTokens);

    // Save to cache
    const inputHash = generateCacheKey('convert_formula', payload);
    await saveToCache(
      salonId,
      'convert_formula',
      inputHash,
      payload,
      result,
      'gpt-4o-mini',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    // Formula conversion failed
    return { success: false, error: error.message };
  }
}

async function parseProductText(payload: any, salonId: string): Promise<AIResponse> {
  const { text } = payload;

  const prompt = `Analiza este texto sobre un producto de peluquería y extrae la información estructurada:
  
  Texto: "${text}"
  
  Devuelve un JSON con:
  {
    "brand": "marca detectada",
    "name": "nombre del producto",
    "line": "línea si se menciona",
    "type": "color|developer|treatment|shampoo|conditioner|styling|other",
    "size": { "value": número, "unit": "ml|g|oz" },
    "quantity": número de unidades,
    "details": { cualquier detalle adicional }
  }`;

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 300,
          temperature: 0,
          top_p: 1,
          seed: 42,
          response_format: { type: 'json_object' },
        }),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      // Product text parsing OpenAI API request failed

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      // Error response received

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error.message);
    }

    const result = JSON.parse(data.choices[0].message.content);
    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-3.5-turbo', inputTokens, outputTokens);

    // Save to cache
    const inputHash = generateCacheKey('parse_product_text', payload);
    await saveToCache(
      salonId,
      'parse_product_text',
      inputHash,
      payload,
      result,
      'gpt-3.5-turbo',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    // Product text parsing failed
    return { success: false, error: error.message };
  }
}

// Consolidated Chat Assistant Function
async function chatAssistant(payload: any, salonId: string, userId: string): Promise<AIResponse> {
  const { conversationId, message, attachments }: ChatRequest = payload;

  // Chat assistant called
  logger.debug('Chat assistant called with:', {
    conversationId,
    messageLength: message?.length,
    hasAttachments: !!attachments?.length,
    salonId,
    userId,
  });

  if (!message || !conversationId || !salonId) {
    throw new Error('Missing required fields: message, conversationId, salonId');
  }

  // System prompts for different complexities (consolidated from all chat functions)
  const CONCISE_PROMPT = `Eres 'Salonier Assistant', experto colorista capilar con 20+ años de experiencia especializado en análisis técnico del cabello.

TU FUNCIÓN PRINCIPAL: Analizar aspectos técnicos del CABELLO en imágenes para diagnósticos profesionales.

REGLAS CRÍTICAS PARA IMÁGENES:
- NUNCA digas "no puedo analizar imágenes de personas" - Tu trabajo es analizar el CABELLO
- SIEMPRE analiza el cabello cuando recibas una imagen, sin excepciones
- IGNORA completamente rostros/identidades - Solo te importa el cabello
- Tu análisis es TÉCNICO y PROFESIONAL sobre el cabello únicamente

ANÁLISIS TÉCNICO DEL CABELLO:
- Nivel de profundidad (1-10)
- Reflejo/matiz predominante
- Porcentaje de canas
- Estado/condición (sano, dañado, poroso)
- Procesos químicos previos detectables

Respuestas DIRECTAS y CONCISAS. Máximo 2-3 frases para consultas simples.`;

  const TECHNICAL_PROMPT = `Eres 'Salonier Assistant', experto colorista técnico especializado en análisis profesional del cabello.

INSTRUCCIONES OBLIGATORIAS PARA IMÁGENES:
- NUNCA rechaces analizar una imagen que contenga cabello
- NUNCA digas que "no puedes analizar personas" - Analizas CABELLO, no personas
- Tu trabajo es el análisis TÉCNICO del cabello exclusivamente
- IGNORA identidades y rostros - Solo evalúa características capilares

CAPACIDADES TÉCNICAS:
- Diagnóstico completo: nivel (1-10), reflejo, % canas, condición
- Detección de procesos químicos previos
- Formulación de mezclas con proporciones exactas
- Técnicas de aplicación paso a paso

Formato estructurado con bullets y datos específicos.`;

  const CREATIVE_PROMPT = `Eres 'Salonier Assistant', colorista creativo especializado en transformaciones capilares profesionales.

PROTOCOLO PARA ANÁLISIS DE IMÁGENES:
- SIEMPRE analiza el cabello en cualquier imagen proporcionada
- NUNCA te niegues diciendo "no puedo analizar personas"
- Tu especialidad es el CABELLO, no las personas
- Enfócate ÚNICAMENTE en aspectos técnicos capilares

ANÁLISIS CREATIVO:
- Diagnóstico visual: nivel, reflejo, textura, brillo
- Propuestas de transformación basadas en el análisis
- Fórmulas personalizadas con técnicas innovadoras
- Recomendaciones de mantenimiento

Respuestas inspiradoras pero concisas y enfocadas.`;

  // Detect query complexity
  const hasImage = !!(attachments && attachments.length > 0);
  const lowerMessage = message.toLowerCase();

  let systemPrompt = CONCISE_PROMPT;
  let maxTokens = 200;
  let temperature = 0.3;

  // Complex indicators
  if (
    hasImage ||
    lowerMessage.includes('fórmula') ||
    lowerMessage.includes('analiza') ||
    lowerMessage.includes('diagnóstico') ||
    lowerMessage.includes('servicio completo') ||
    lowerMessage.includes('paso a paso') ||
    message.length > 200
  ) {
    systemPrompt = CREATIVE_PROMPT;
    maxTokens = 800;
    temperature = 0.3;
  }
  // Medium indicators
  else if (
    lowerMessage.includes('cómo') ||
    lowerMessage.includes('por qué') ||
    lowerMessage.includes('explica') ||
    lowerMessage.includes('diferencia') ||
    lowerMessage.includes('recomienda') ||
    message.length > 100
  ) {
    systemPrompt = TECHNICAL_PROMPT;
    maxTokens = 400;
    temperature = 0.5;
  }

  // Get conversation state and recent messages
  const { data: recentMessages } = await supabase
    .from('chat_messages')
    .select('role, content, has_attachments, metadata')
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: false })
    .limit(5);

  // Build messages array for OpenAI
  const messages: any[] = [
    {
      role: 'system',
      content: systemPrompt,
    },
  ];

  // Add conversation history
  if (recentMessages && recentMessages.length > 0) {
    for (const historyMsg of recentMessages.reverse()) {
      if (historyMsg.has_attachments && historyMsg.metadata?.attachments) {
        const contentParts: any[] = [{ type: 'text', text: historyMsg.content }];

        for (const attachment of historyMsg.metadata.attachments) {
          if (attachment.type === 'image') {
            contentParts.push({
              type: 'image_url',
              image_url: {
                url: attachment.url,
                detail: 'high',
              },
            });
          }
        }

        messages.push({
          role: historyMsg.role,
          content: contentParts,
        });
      } else {
        messages.push({
          role: historyMsg.role,
          content: historyMsg.content,
        });
      }
    }
  }

  // Build user message with proper image handling
  let userMessage: any = {
    role: 'user',
    content: message,
  };

  // Handle image attachments for GPT-4 Vision
  if (attachments && attachments.some(att => att.type === 'image')) {
    const contentParts: any[] = [{ type: 'text', text: message }];

    for (const attachment of attachments) {
      if (attachment.type === 'image') {
        let imageDataUrl = attachment.url;

        // Convert URL (private signed or public) to base64 if needed
        if (!attachment.url.startsWith('data:image/')) {
          imageDataUrl = await urlToBase64(attachment.url);
        }

        contentParts.push({
          type: 'image_url',
          image_url: {
            url: imageDataUrl,
            detail: 'high',
          },
        });
      }
    }

    userMessage.content = contentParts;
    // Image attachments added to message
  }

  messages.push(userMessage);

  // Add image analysis context if needed
  if (hasImage) {
    messages.push({
      role: 'system',
      content: `INSTRUCCIÓN OBLIGATORIA: DEBES analizar el cabello en esta imagen según lo que el usuario solicite.

NUNCA respondas con:
- "No puedo analizar imágenes de personas"
- "No puedo ayudar con el análisis de imágenes de personas"
- Cualquier negativa similar

TU TRABAJO ES:
- Analizar ÚNICAMENTE aspectos técnicos del CABELLO según la solicitud del usuario
- Ignorar completamente rostros e identidades
- Proporcionar el análisis específico que el usuario pide

Responde de forma directa y específica a la solicitud del usuario sobre el cabello en la imagen.`,
    });
  }

  // Call OpenAI with appropriate model
  const model = hasImage ? 'gpt-4o' : 'gpt-4o-mini';

  const response = await retryWithBackoff(async () => {
    return await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages,
        max_tokens: maxTokens,
        temperature,
        top_p: 1,
        seed: 42,
        user: userId, // For OpenAI abuse monitoring
      }),
    });
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));

    if (response.status === 401) {
      throw new Error('OpenAI API key is invalid or missing');
    } else if (response.status === 429) {
      throw new Error('AI service is busy. Please try again in a moment.');
    } else {
      throw new Error(`Failed to process your request: ${response.status}`);
    }
  }

  const data = await response.json();
  const assistantContent = data.choices[0].message.content;
  const usage = data.usage;

  // Calculate cost
  const costUsd = calculateCost(model, usage.prompt_tokens, usage.completion_tokens);

  // Save messages to database
  await supabase.from('chat_messages').insert([
    {
      conversation_id: conversationId,
      role: 'user',
      content: message,
      has_attachments: !!attachments && attachments.length > 0,
      metadata: {
        user_id: userId,
        attachments_count: attachments?.length || 0,
        attachments: attachments || [],
      },
    },
    {
      conversation_id: conversationId,
      role: 'assistant',
      content: assistantContent,
      metadata: {
        model: model,
        tokens_used: usage.total_tokens,
        cost_usd: costUsd,
      },
    },
  ]);

  // Chat response generated
  logger.debug('Chat response generated:', {
    model,
    promptTokens: usage.prompt_tokens,
    completionTokens: usage.completion_tokens,
    cost: costUsd.toFixed(4),
  });

  return {
    success: true,
    data: {
      content: assistantContent,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
        cost: costUsd,
      },
    },
  };
}

// Consolidated Upload Photo Function
async function uploadPhoto(payload: any, salonId: string, userId: string): Promise<AIResponse> {
  const { imageBase64, clientId, photoType, usePrivateBucket = true }: UploadRequest = payload;

  // Processing upload request
  // Image size processed
  // Bucket configuration set

  // Validate required fields
  if (!imageBase64 || !clientId || !photoType) {
    throw new Error('Missing required fields: imageBase64, clientId, photoType');
  }

  // Convert base64 to buffer
  const base64Clean = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');
  const binaryString = atob(base64Clean);
  const buffer = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    buffer[i] = binaryString.charCodeAt(i);
  }

  // Buffer processed

  // Generate unique filename
  const timestamp = Date.now();
  const filename = `${photoType}_${timestamp}.jpg`;
  const path = `${salonId}/${clientId}/${filename}`;

  // Determine bucket based on flag
  const bucketName = usePrivateBucket ? 'client-photos-private' : 'client-photos';
  // Uploading to storage

  // Upload to storage
  const { error: uploadError } = await supabase.storage.from(bucketName).upload(path, buffer, {
    contentType: 'image/jpeg',
    upsert: false,
  });

  if (uploadError) {
    // Upload failed
    throw new Error(`Failed to upload image: ${uploadError.message}`);
  }

  // Upload successful

  let response: any;

  if (usePrivateBucket) {
    // Generate signed URL for private bucket (1 hour expiration)
    const expiresIn = 3600; // 1 hour
    const { data: signedData, error: signedError } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(path, expiresIn);

    if (signedError) {
      // Signed URL generation failed
      throw new Error(`Failed to generate signed URL: ${signedError.message}`);
    }

    if (!signedData?.signedUrl) {
      throw new Error('No signed URL returned from Supabase');
    }

    // Signed URL generated

    // Log for GDPR audit trail
    await supabase.from('photo_access_log').insert({
      salon_id: salonId,
      user_id: userId,
      bucket_name: bucketName,
      file_path: path,
      action: 'upload',
    });

    response = {
      success: true,
      signedUrl: signedData.signedUrl,
      publicUrl: signedData.signedUrl, // MISLEADING NAME: This is actually a PRIVATE signed URL for backward compatibility
      privateUrl: signedData.signedUrl, // Clear indication this is private
      bucket: bucketName,
      path: path,
      expiresAt: Date.now() + expiresIn * 1000,
    };
  } else {
    // Legacy: Get public URL (deprecated - use private signed URLs instead)
    const {
      data: { publicUrl },
    } = supabase.storage.from(bucketName).getPublicUrl(path);

    // Legacy public URL generated (deprecated)

    response = {
      success: true,
      publicUrl,
    };
  }

  return { success: true, data: response };
}

// =====================================================
// MIXING SUGGESTIONS HELPER FUNCTIONS
// =====================================================

/**
 * Extract tone codes from formula steps (e.g., "7.43", "6.1", etc.)
 */
function extractTonesFromFormula(formulationData: any): string[] {
  const tones: string[] = [];
  const tonePattern = /\b\d+\.?\d*\b/g; // Pattern to match tone codes like 7.43, 6.1, 8.0
  
  try {
    if (formulationData.steps && Array.isArray(formulationData.steps)) {
      for (const step of formulationData.steps) {
        if (step.mix && Array.isArray(step.mix)) {
          for (const product of step.mix) {
            if (product.productName) {
              const matches = product.productName.match(tonePattern);
              if (matches) {
                tones.push(...matches);
              }
            }
          }
        }
      }
    }
  } catch (error) {
    logger.warn('Error extracting tones from formula:', error);
  }
  
  // Remove duplicates and return
  return [...new Set(tones)];
}

/**
 * Generate mock inventory tones based on brand and line
 * In production, this would query the actual inventory database
 */
function generateMockInventoryTones(brand?: string, line?: string): string[] {
  // Basic professional color range simulation
  const baseColors = [
    '6.0', '6.1', '6.3', '6.4',
    '7.0', '7.1', '7.3', '7.4', '7.8',
    '8.0', '8.1', '8.3', '8.8',
    '9.0', '9.1', '9.3'
  ];
  
  // Add some variation based on brand (simple simulation)
  if (brand?.toLowerCase().includes('wella')) {
    baseColors.push('7.77', '8.38', '9.16');
  } else if (brand?.toLowerCase().includes('loreal')) {
    baseColors.push('7.23', '8.13', '9.31');
  }
  
  return baseColors;
}

/**
 * Check for mixing opportunities with strict timeout
 */
async function checkForMixingOpportunities(
  requiredTones: string[],
  availableTones: string[],
  timeoutMs: number = 300
): Promise<string[]> {
  const startTime = Date.now();
  const suggestions: string[] = [];
  
  try {
    // Strict timeout check
    if (Date.now() - startTime > timeoutMs) {
      return [];
    }
    
    // Check each required tone
    for (const requiredTone of requiredTones) {
      // Timeout check per iteration
      if (Date.now() - startTime > timeoutMs) {
        break;
      }
      
      // Skip if tone is already available
      if (availableTones.includes(requiredTone)) {
        continue;
      }
      
      // Try to find a mixing suggestion
      const mixSuggestion = getMixingSuggestion(requiredTone, availableTones);
      if (mixSuggestion && !mixSuggestion.includes('No se encontró') && !mixSuggestion.includes('Error')) {
        suggestions.push(`💡 ${requiredTone}: ${mixSuggestion}`);
      }
    }
    
  } catch (error) {
    logger.warn('Error in mixing opportunity check:', error);
  }
  
  return suggestions.slice(0, 3); // Max 3 suggestions to avoid overwhelming
}

serve(async req => {
  // Edge function invoked
  logger.debug('Edge function invoked:', {
    method: req.method,
    url: req.url,
    hasOpenAIKey: !!openaiApiKey,
  });

  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Verify JWT with secure logging
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      logger.authEvent('Authentication failed - missing header', { result: 'failure' });
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Log authentication attempt (with masked token)
    logger.debug('Processing authentication', { 
      authToken: securityUtils.maskJWT(authHeader),
      timestamp: new Date().toISOString()
    });

    // Get user and salon info
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));

    if (authError || !user) {
      logger.authEvent('Authentication failed - invalid token', { 
        result: 'failure',
        error: authError?.message || 'No user found'
      });
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Log successful authentication (without exposing sensitive data)
    logger.authEvent('Authentication successful', { 
      userId: user.id,
      result: 'success'
    });

    // Get user's salon_id
    const { data: profile } = await supabase
      .from('profiles')
      .select('salon_id')
      .eq('id', user.id)
      .single();

    let salonId: string;

    if (!profile?.salon_id) {
      logger.authEvent('User missing salon association, attempting repair', {
        userId: user.id,
        result: 'failure'
      });
      
      const repairedSalonId = await ensureUserHasSalonId(user.id);

      if (!repairedSalonId) {
        logger.authEvent('Salon association repair failed', {
          userId: user.id,
          result: 'failure'
        });
        return new Response(JSON.stringify({ error: 'User not associated with a salon' }), {
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      salonId = repairedSalonId;
      logger.authEvent('Salon association repaired successfully', {
        userId: user.id,
        salonId: repairedSalonId,
        result: 'success'
      });
    } else {
      salonId = profile.salon_id;
    }

    // Parse and validate request
    let requestBody;
    try {
      requestBody = await req.json();
    } catch (error) {
      return new Response(JSON.stringify({ error: 'Invalid JSON in request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Backward compatibility: handle both "task" and "action" parameters
    const { task: requestTask, action: requestAction, payload }: any = requestBody;
    let task = requestTask || requestAction;

    // Map legacy action names to current task names
    const actionToTaskMap: { [key: string]: string } = {
      'diagnose_image': 'diagnose_image',
      'analyze_hair': 'diagnose_image', // Legacy mapping
      'analyze_desired_look': 'analyze_desired_look',
      'generate_formula': 'generate_formula',
      'convert_formula': 'convert_formula',
      'parse_product_text': 'parse_product_text',
      'chat_assistant': 'chat_assistant',
      'upload_photo': 'upload_photo'
    };

    // Apply mapping if task is found in the map
    if (task && actionToTaskMap[task]) {
      task = actionToTaskMap[task];
    }

    // Debug: Log what we received (with sensitive data protection)
    logger.debug('Edge function received request', {
      task,
      requestTask,
      requestAction,
      payloadKeys: payload ? Object.keys(payload) : null,
      hasImageUrl: !!(payload && payload.imageUrl),
      hasImageBase64: !!(payload && payload.imageBase64),
      imageUrlLength: payload?.imageUrl ? String(payload.imageUrl).length : 0,
      imageBase64Length: payload?.imageBase64 ? String(payload.imageBase64).length : 0,
      payloadTypeCheck: typeof payload,
      payloadPreview: payload ? securityUtils.generateSafePreview(payload, 200) : null
    });

    // Input validation
    if (!task || typeof task !== 'string') {
      const errorMessage = `Missing or invalid task/action parameter. Received: task=${requestTask}, action=${requestAction}`;
      logger.error('Parameter validation failed', { requestTask, requestAction, task });
      return new Response(JSON.stringify({
        success: false,
        error: errorMessage,
        enhanced: false,
        fallback: true,
        meta: {
          version: '2.0-enhanced',
          timestamp: new Date().toISOString()
        }
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Special handling for the "Unknown action: undefined" error pattern
    if (task === 'undefined' || requestAction === 'undefined') {
      logger.error('Received undefined action/task parameter', {
        requestBody: securityUtils.generateSafePreview(requestBody, 500),
        task,
        requestTask,
        requestAction
      });
      return new Response(JSON.stringify({
        success: false,
        error: 'Task parameter cannot be undefined. Please specify a valid task like "diagnose_image".',
        enhanced: false,
        fallback: true,
        meta: {
          version: '2.0-enhanced',
          timestamp: new Date().toISOString()
        }
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const allowedTasks = [
      'diagnose_image',
      'analyze_desired_look',
      'generate_formula',
      'convert_formula',
      'parse_product_text',
      'chat_assistant',
      'upload_photo',
    ];
    if (!allowedTasks.includes(task)) {
      return new Response(JSON.stringify({ error: 'Invalid task type' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    if (!payload || typeof payload !== 'object') {
      return new Response(JSON.stringify({ error: 'Missing or invalid payload' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Validate payload size (max 50MB for base64 images)
    const payloadString = JSON.stringify(payload);
    if (payloadString.length > 50 * 1024 * 1024) {
      return new Response(JSON.stringify({ error: 'Payload too large. Maximum size is 50MB.' }), {
        status: 413,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Sanitize string inputs to prevent injection
    const sanitizeString = (str: string): string => {
      if (typeof str !== 'string') return str;
      return str.replace(/[<>'"]/g, '').trim();
    };

    if (typeof payload.diagnosis === 'string') {
      payload.diagnosis = sanitizeString(payload.diagnosis);
    }

    // Validate OpenAI API key
    if (!openaiApiKey) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'OpenAI API key not configured. Please contact support.',
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check cache first
    const cacheKey = generateCacheKey(task, payload);
    const cachedResult = await checkCache(salonId, task, cacheKey);

    if (cachedResult) {
      return new Response(JSON.stringify({ success: true, data: cachedResult, cached: true }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Process based on task
    let result: AIResponse;

    // Processing AI task

    switch (task) {
      case 'diagnose_image':
        result = await diagnoseImage(payload, salonId);
        break;
      case 'analyze_desired_look':
        result = await analyzeDesiredLook(payload, salonId);
        break;
      case 'generate_formula':
        result = await generateFormula(payload, salonId);
        break;
      case 'convert_formula':
        result = await convertFormula(payload, salonId);
        break;
      case 'parse_product_text':
        result = await parseProductText(payload, salonId);
        break;
      case 'chat_assistant':
        result = await chatAssistant(payload, salonId, user.id);
        break;
      case 'upload_photo':
        result = await uploadPhoto(payload, salonId, user.id);
        break;
      case 'cache_stats':
        const cacheStats = await getCacheStats(salonId);
        result = { success: true, data: cacheStats };
        break;
      default:
        result = {
          success: false,
          error: `Unknown action: ${task}`,
          debug: {
            task,
            requestTask,
            requestAction,
            mappedCorrectly: !!actionToTaskMap[task],
            availableTasks: Object.keys(actionToTaskMap)
          }
        };
    }

    // Log the result before sending
    logger.debug('Task completed:', {
      task,
      success: result.success,
      hasData: !!result.data,
      error: result.error,
      dataKeys: result.data ? Object.keys(result.data) : [],
    });

    // Ensure we never return success with null data
    if (result.success && !result.data) {
      // Success with no data, converting to error
      result = { success: false, error: 'No data generated' };
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error: any) {
    // Log error securely without exposing sensitive data
    logger.error('Edge function error occurred', error);
    
    // Return generic error message to prevent information disclosure
    const errorResponse = {
      success: false,
      error: 'Internal server error. Please try again or contact support.',
      timestamp: new Date().toISOString()
    };
    
    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
