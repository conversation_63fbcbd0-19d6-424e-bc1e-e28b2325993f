/**
 * SAL<PERSON>IER BEAUTY TECH COLOR SYSTEM (CLAUDE-003)
 *
 * Implements 90/10 Beauty Minimalism Strategy:
 * • 90% sophisticated neutral foundation for professional trust and readability
 * • 10% refined beauty colors for strategic emphasis and brand identity
 * • Claude-inspired density and information hierarchy
 *
 * TRANSFORMATION FROM V2.1:
 * • Migrated from vibrant salon colors to refined beauty minimalism
 * • Implemented neutral-first approach with strategic color usage
 * • Maintained backward compatibility while introducing new system
 * • WCAG 2.1 AA compliance with enhanced readability ratios
 */

// =============================================================================
// NEUTRAL FOUNDATION (90% Usage) - Professional Trust Building
// =============================================================================

/**
 * CLAUDE-INSPIRED NEUTRALS
 * The backbone of our interface. These colors build the professional trust
 * that beauty experts expect from premium tools.
 */
const neutrals = {
  // Light to Dark Spectrum (WCAG AA Compliant)
  pure: '#FFFFFF', // Pure backgrounds, cards, modals
  pearl: '#FEFEFE', // Main app background (warmer than pure white)
  cloud: '#F8FAFC', // Secondary surfaces, input backgrounds
  mist: '#E2E8F0', // Subtle borders, dividers
  whisper: '#CBD5E1', // Light borders, inactive states
  silver: '#94A3B8', // Secondary text, placeholders (4.52:1 ratio)
  slate: '#64748B', // Tertiary text, captions (6.81:1 ratio)
  charcoal: '#371E30', // Primary text, headings - updated to dark-purple for harmony (13.2:1 ratio)
} as const;

// =============================================================================
// BEAUTY COLORS (10% Usage) - Strategic Brand Emphasis
// =============================================================================

/**
 * REFINED BEAUTY PALETTE - PINK-PURPLE HARMONY
 * Intentionally limited colors for strategic emphasis only.
 * Each color has specific purpose and emotional connection to beauty profession.
 * Updated to sophisticated pink-purple palette for enhanced beauty-tech identity.
 */
const beautyColors = {
  // Primary Brand - Razzle Dazzle Rose (sophisticated pink-purple)
  'razzle-dazzle-rose': {
    50: '#FDF4F8', // Ultra-light for subtle backgrounds
    100: '#FCE7F0', // Light for hover states
    200: '#F9CAE1', // Medium-light for disabled states
    300: '#F3A3CC', // Medium for inactive elements
    400: '#E77BB7', // Medium-dark for secondary emphasis
    500: '#DF57BC', // Primary brand color, main CTAs
    600: '#A03E99', // Plum - pressed states
    700: '#7D2E78', // Darker for high contrast needs
    800: '#5A1E5A', // Very dark for text on light backgrounds
    900: '#3D1142', // Maximum contrast dark
  },

  // Secondary - Salmon Pink (warm coral-pink)
  'salmon-pink': {
    50: '#FFF5F6', // Ultra-light pink backgrounds
    100: '#FEEAEC', // Light pink hover states
    200: '#FDD4D9', // Medium-light progressive tones
    300: '#FCBDC5', // Medium progressive tones
    400: '#F8A0B1', // Medium-dark progressive tones
    500: '#F59CA9', // Main secondary color
    600: '#F6828C', // Light-coral - pressed states
    700: '#E8677A', // Progressive darker tone
    800: '#CC5B6E', // Very dark for contrast
    900: '#A63F52', // Maximum contrast dark
  },

  // Professional - Sophisticated Sage (refined from glaucous #7d82b8) - KEPT FOR PROFESSIONAL ACTIONS
  sage: {
    50: '#F0FDFA', // Success backgrounds, subtle highlights
    100: '#CCFBF1', // Light success accents
    300: '#5EEAD4', // Disabled success states
    500: '#14B8A6', // Professional actions, growth, success
    600: '#0D9488', // Pressed success states
    700: '#0F766E', // Dark mode professional, strong success
  },

  // Backward Compatibility Aliases (maintain existing names)
  amethyst: {
    50: '#FDF4F8', // Maps to razzle-dazzle-rose[50]
    100: '#FCE7F0', // Maps to razzle-dazzle-rose[100]
    200: '#F9CAE1', // Maps to razzle-dazzle-rose[200]
    300: '#F3A3CC', // Maps to razzle-dazzle-rose[300]
    400: '#E77BB7', // Maps to razzle-dazzle-rose[400]
    500: '#DF57BC', // Maps to razzle-dazzle-rose[500] - PRIMARY BRAND
    600: '#A03E99', // Maps to razzle-dazzle-rose[600] - PLUM
    700: '#7D2E78', // Maps to razzle-dazzle-rose[700]
    800: '#5A1E5A', // Maps to razzle-dazzle-rose[800]
    900: '#3D1142', // Maps to razzle-dazzle-rose[900]
  },

  rose: {
    50: '#FFF5F6', // Maps to salmon-pink[50]
    100: '#FEEAEC', // Maps to salmon-pink[100]
    200: '#FDD4D9', // Maps to salmon-pink[200]
    300: '#FCBDC5', // Maps to salmon-pink[300]
    400: '#F8A0B1', // Maps to salmon-pink[400]
    500: '#F59CA9', // Maps to salmon-pink[500] - SALMON PINK
    600: '#F6828C', // Maps to salmon-pink[600] - LIGHT CORAL
    700: '#E8677A', // Maps to salmon-pink[700]
    800: '#CC5B6E', // Maps to salmon-pink[800]
    900: '#A63F52', // Maps to salmon-pink[900]
  },
} as const;

// =============================================================================
// SEMANTIC SYSTEM COLORS
// =============================================================================

const semanticColors = {
  // Status Colors (updated for pink-purple harmony)
  success: beautyColors.sage[500], // Professional teal for success (kept for trust)
  warning: '#F59E0B', // Refined amber for warnings (kept for clarity)
  error: '#EF4444', // Clean red for errors (kept for universal recognition)
  info: beautyColors['razzle-dazzle-rose'][500], // New primary brand purple-pink for information
} as const;

// =============================================================================
// BACKWARD COMPATIBILITY LAYER
// =============================================================================

/**
 * LEGACY COLOR MAPPINGS - PINK-PURPLE UPDATE
 * Maintains compatibility while gradually migrating to new pink-purple system.
 * Updated mappings reflect new salmon-pink and razzle-dazzle-rose palette.
 */

// Direct legacy mappings (updated for pink-purple palette)
const _finn = neutrals.charcoal; // #613f75 → #371E30 (dark-purple for harmony)
const _brightPinkCrayola = beautyColors['salmon-pink'][500]; // #ef798a → #F59CA9 (salmon-pink)
const _glaucous = beautyColors['razzle-dazzle-rose'][500]; // #7d82b8 → #DF57BC (razzle-dazzle-rose)
const _melon = beautyColors['salmon-pink'][50]; // Soft background variant in new salmon-pink
const _fairyTale = neutrals.cloud; // Subtle background surface (kept neutral)

// Refined neutral aliases
const _premiumBlack = neutrals.charcoal;
const _warmGray = neutrals.slate;
const _neutralSlate = neutrals.charcoal; // Primary text
const _neutralGray = neutrals.slate; // Secondary text
const _neutralLight = neutrals.silver; // Tertiary text/disabled
const _neutralBorder = neutrals.mist; // Subtle borders
const _neutralSurface = neutrals.cloud; // Cards/elevated surfaces
const _neutralBackground = neutrals.pure; // Pure background

// Sophisticated accent touches (replacing heavy color usage)
const _warmBeige = neutrals.cloud; // Neutral replacement for warm backgrounds
const _softLavender = neutrals.pearl; // Subtle neutral replacement
const _dustyRose = beautyColors.rose[50]; // Gentle pink-tinted backgrounds

// Legacy semantic mappings
const _successGreen = semanticColors.success;
const _warningCoral = semanticColors.warning;
const _errorRose = semanticColors.error;
const _infoBlue = semanticColors.info;

// Additional backward compatibility (updated for pink-purple)
const _roseLight = beautyColors['salmon-pink'][50]; // Updated to salmon-pink light
const _coralSecondary = neutrals.cloud; // Kept neutral for professional look
const _beigeLight = neutrals.cloud; // Kept neutral
const _creamWhite = neutrals.pure; // Pure white maintained

// Primary beauty brand colors (strategic 10% usage - UPDATED)
const _beautyPurple = beautyColors['razzle-dazzle-rose'][500]; // Primary brand - razzle-dazzle-rose
const _beautyRose = beautyColors['salmon-pink'][500]; // Secondary brand - salmon-pink
const _beautyTeal = beautyColors.sage[500]; // Professional accent (kept for trust)

export default {
  common: {
    transparent: 'transparent',
    black: '#000000',
    shadowColor: '#000000',
  },
  light: {
    // =============================================================================
    // TEXT HIERARCHY (90% Neutral) - Claude-Inspired Readability
    // =============================================================================
    text: neutrals.charcoal, // Primary text - 12.85:1 contrast ratio
    textSecondary: neutrals.slate, // Secondary text - 6.81:1 contrast ratio
    textLight: neutrals.pure, // Light text on dark backgrounds

    // Additional text variants for comprehensive hierarchy
    textTertiary: neutrals.slate, // Captions, placeholders - FIXED: 4.76:1 ratio (was silver 2.56:1)
    textDisabled: neutrals.slate, // Disabled text state - FIXED for accessibility
    textInverse: neutrals.pure, // White text on colored backgrounds
    textAccessible: neutrals.charcoal, // High contrast text for critical information (15.10:1)

    // =============================================================================
    // BACKGROUND FOUNDATION (90% Neutral) - Professional Trust
    // =============================================================================
    background: neutrals.pearl, // Main app background (warmer than pure)
    backgroundSecondary: neutrals.cloud, // Cards, elevated surfaces
    backgroundTertiary: neutrals.pure, // Pure white for inputs, modals
    backgroundDark: neutrals.charcoal, // Dark backgrounds for headers

    // Surface variations
    surface: neutrals.cloud, // Elevated surfaces, input backgrounds
    surfaceElevated: neutrals.pure, // Higher elevation surfaces

    // =============================================================================
    // BRAND COLORS (10% Strategic Usage) - Beauty Tech Identity
    // =============================================================================
    primary: beautyColors.amethyst[600], // Primary brand actions - FIXED: Use plum (#A03E99) for 5.76:1 ratio
    primaryDark: beautyColors.amethyst[700], // Pressed states - darker for better contrast
    primaryLight: beautyColors.amethyst[300], // Disabled states (#F3A3CC)
    primaryBackground: beautyColors.amethyst[50], // Subtle brand backgrounds

    secondary: beautyColors.rose[500], // Secondary actions (#EC4899)
    secondaryDark: beautyColors.rose[600], // Pressed secondary states
    secondaryLight: beautyColors.rose[300], // Disabled secondary states
    secondaryBackground: beautyColors.rose[50], // Subtle secondary backgrounds

    accent: beautyColors.sage[500], // Professional accent (#14B8A6)
    accentDark: beautyColors.sage[600], // Pressed accent states
    accentLight: beautyColors.sage[300], // Disabled accent states
    accentBackground: beautyColors.sage[50], // Subtle accent backgrounds

    // =============================================================================
    // UI ELEMENTS (90% Neutral Foundation) - Minimal and Professional
    // =============================================================================
    card: neutrals.pure, // Clean white cards
    cardSecondary: neutrals.cloud, // Subtle card variations
    surfaceVariant: neutrals.pure, // Alternative surfaces

    // Border System (refined neutral borders)
    border: neutrals.mist, // Standard borders (#E2E8F0)
    borderLight: neutrals.whisper, // Lighter borders (#CBD5E1)
    borderSubtle: neutrals.whisper, // Very subtle borders
    borderFocus: beautyColors.amethyst[600], // Focus state - FIXED: Use plum for better contrast

    // Navigation (professional and understated)
    tabIconDefault: neutrals.slate, // Inactive tabs - FIXED: Use slate for better contrast
    tabIconSelected: beautyColors.amethyst[600], // Active tabs - FIXED: Use plum for accessibility
    tint: beautyColors.amethyst[600], // System tint - FIXED: Use plum for better contrast
    tabBackground: neutrals.pure, // Tab bar background

    // =============================================================================
    // SEMANTIC STATUS COLORS - Refined System Feedback
    // =============================================================================
    success: semanticColors.success, // Professional teal (#14B8A6)
    successLight: beautyColors.sage[300], // Light success variant
    successBackground: beautyColors.sage[50], // Success background

    warning: semanticColors.warning, // Refined amber (#F59E0B)
    warningLight: '#FCD34D', // Light warning variant
    warningBackground: 'rgba(245, 158, 11, 0.1)', // Subtle warning background
    warningBorder: '#FCD34D', // Warning border
    warningText: neutrals.charcoal, // High contrast warning text

    error: semanticColors.error, // Clean red (#EF4444)
    errorLight: '#FCA5A5', // Light error variant
    errorBackground: 'rgba(239, 68, 68, 0.1)', // Subtle error background
    errorBorder: '#FCA5A5', // Error border
    errorText: neutrals.charcoal, // High contrast error text

    info: semanticColors.info, // Brand purple for info
    infoBackground: beautyColors.amethyst[50], // Info background

    danger: semanticColors.error, // Consistent with error

    // =============================================================================
    // GRAYSCALE SYSTEM (90% Usage) - Claude-Inspired Hierarchy
    // =============================================================================
    gray: neutrals.slate, // Standard gray (#64748B)
    grayLight: neutrals.silver, // Light gray (#94A3B8)
    grayDark: neutrals.charcoal, // Dark gray (#1E293B)
    lightGray: neutrals.cloud, // Very light backgrounds (#F8FAFC)
    darkGray: neutrals.charcoal, // Dark emphasis
    placeholderGray: neutrals.silver, // Placeholder text
    disabledGray: neutrals.silver, // Disabled elements

    // Special Action Colors (strategic 10% usage)
    redButton: beautyColors.rose[500], // Destructive actions in elegant rose

    // =============================================================================
    // SPECIAL UI STATES (Refined and Professional)
    // =============================================================================
    notification: neutrals.cloud, // Neutral for notifications (90% rule)
    highlight: beautyColors.amethyst[50], // Subtle brand highlight

    // AI Analysis Colors (trust-building palette with 10% beauty colors)
    aiProcessing: beautyColors.amethyst[500], // Brand purple for AI
    aiSuccess: beautyColors.sage[500], // Professional teal for success
    aiWarning: semanticColors.warning, // Consistent amber
    aiError: semanticColors.error, // Consistent red
    aiBackground: neutrals.cloud, // Neutral AI backgrounds

    // Privacy and Security (trustworthy neutrals with accent)
    privacy: beautyColors.sage[500], // Professional teal
    security: beautyColors.amethyst[500], // Brand purple
    privacyBackground: neutrals.cloud, // Neutral backgrounds

    // Quality Indicators (clear semantic hierarchy)
    qualityExcellent: beautyColors.sage[500], // Teal excellence
    qualityGood: beautyColors.amethyst[500], // Purple good quality
    qualityPoor: semanticColors.error, // Consistent red for poor
    qualityBackground: neutrals.cloud, // Neutral quality backgrounds

    // Progress Indicators (minimal and clean)
    progressBackground: neutrals.mist, // Subtle track background
    progressFill: beautyColors.amethyst[500], // Brand color for progress
    progressBarBackground: neutrals.mist, // Consistent track
    progressBarFill: beautyColors.amethyst[500], // Active progress

    // Timeline and Special UI (beauty-minimalist consistent)
    timelineBackground: neutrals.cloud, // Neutral timeline background
    pauseButton: beautyColors.rose[500], // Elegant pause in rose
    disabledText: neutrals.silver, // Consistent disabled text

    // =============================================================================
    // SHADOW SYSTEM (Subtle and Professional) - Updated for Dark-Purple Harmony
    // =============================================================================
    shadowColor: neutrals.charcoal, // Dark-purple shadow color for harmony
    shadowLight: 'rgba(55, 30, 48, 0.04)', // Very subtle dark-purple shadows
    shadowMedium: 'rgba(55, 30, 48, 0.08)', // Medium elevation dark-purple
    shadowStrong: 'rgba(55, 30, 48, 0.12)', // Modal shadows dark-purple

    // =============================================================================
    // OVERLAY AND TRANSPARENCY SYSTEM (Beauty Minimalism)
    // =============================================================================

    // White Overlay System (refined for overlays and loading states)
    backgroundWithOpacity: 'rgba(254, 254, 254, 0.8)', // Pearl background overlay
    whiteTransparent80: 'rgba(255, 255, 255, 0.8)', // Legacy compatibility
    backgroundOpacity20: 'rgba(255, 255, 255, 0.2)',
    backgroundOpacity50: 'rgba(255, 255, 255, 0.5)',
    backgroundOpacity70: 'rgba(255, 255, 255, 0.7)',
    backgroundOpacity90: 'rgba(255, 255, 255, 0.9)',
    backgroundOpacity95: 'rgba(255, 255, 255, 0.95)',
    loadingBackground: 'rgba(248, 250, 252, 0.95)', // Cloud background for loading

    // Ripple Effects (subtle and elegant) - Updated for pink-purple
    rippleWhite: 'rgba(255, 255, 255, 0.15)', // Subtle white ripple
    rippleRed: 'rgba(239, 68, 68, 0.08)', // Subtle error ripple
    ripplePrimary: 'rgba(223, 87, 188, 0.12)', // Brand ripple effect - razzle-dazzle-rose

    // Modal and Dialog System
    modalOverlay: 'rgba(55, 30, 48, 0.4)', // Dark-purple modal overlay for harmony
    borderTransparent: 'rgba(55, 30, 48, 0.04)', // Subtle transparent borders with dark-purple
    borderGray: neutrals.mist, // Consistent border reference

    // =============================================================================
    // BRAND TRANSPARENCY VARIANTS (10% Strategic Usage) - PINK-PURPLE UPDATE
    // =============================================================================

    // Primary Brand (Razzle Dazzle Rose) Transparencies - Professionally Calibrated
    primaryTransparent05: 'rgba(223, 87, 188, 0.05)', // Very subtle tint for backgrounds
    primaryTransparent10: 'rgba(223, 87, 188, 0.1)', // Light backgrounds, subtle emphasis
    primaryTransparent15: 'rgba(223, 87, 188, 0.15)', // Medium backgrounds, gentle highlighting
    primaryTransparent20: 'rgba(223, 87, 188, 0.2)', // Strong backgrounds, clear emphasis
    primaryTransparent25: 'rgba(223, 87, 188, 0.25)', // Prominent backgrounds
    primaryTransparent30: 'rgba(223, 87, 188, 0.3)', // High contrast backgrounds

    // Secondary Brand (Salmon Pink) Transparencies - Professionally Calibrated
    secondaryTransparent05: 'rgba(245, 156, 169, 0.05)', // Very subtle pink tint
    secondaryTransparent10: 'rgba(245, 156, 169, 0.1)', // Light pink backgrounds
    secondaryTransparent15: 'rgba(245, 156, 169, 0.15)', // Medium pink backgrounds
    secondaryTransparent20: 'rgba(245, 156, 169, 0.2)', // Strong pink backgrounds
    secondaryTransparent25: 'rgba(245, 156, 169, 0.25)', // Prominent pink backgrounds
    secondaryTransparent30: 'rgba(245, 156, 169, 0.3)', // High contrast pink backgrounds

    // Professional Accent (Sage) Transparencies
    accentTransparent05: 'rgba(20, 184, 166, 0.05)', // Very subtle teal tint
    accentTransparent15: 'rgba(20, 184, 166, 0.15)', // Medium teal background
    accentTransparent20: 'rgba(20, 184, 166, 0.2)', // Stronger teal background

    // =============================================================================
    // SEMANTIC TRANSPARENCY VARIANTS (Consistent with Status Colors)
    // =============================================================================

    // Success State Transparencies
    successTransparent10: 'rgba(20, 184, 166, 0.1)', // Light success background
    successTransparent15: 'rgba(20, 184, 166, 0.15)', // Medium success background

    // Warning State Transparencies
    warningTransparent05: 'rgba(245, 158, 11, 0.05)', // Very subtle warning
    warningTransparent10: 'rgba(245, 158, 11, 0.1)', // Light warning background
    warningTransparent15: 'rgba(245, 158, 11, 0.15)', // Medium warning background

    // Error State Transparencies
    errorTransparent05: 'rgba(239, 68, 68, 0.05)', // Very subtle error
    errorTransparent10: 'rgba(239, 68, 68, 0.1)', // Light error background
    errorTransparent15: 'rgba(239, 68, 68, 0.15)', // Medium error background
    errorTransparent20: 'rgba(239, 68, 68, 0.2)', // Stronger error background

    // Info State Transparencies (matches primary brand) - Updated for razzle-dazzle-rose
    infoTransparent10: 'rgba(223, 87, 188, 0.1)', // Light info background - razzle-dazzle-rose
    infoTransparent15: 'rgba(223, 87, 188, 0.15)', // Medium info background - razzle-dazzle-rose

    // =============================================================================
    // SPECIAL BEAUTY TECH UI ELEMENTS (Strategic 10% Beauty Colors) - PINK-PURPLE UPDATE
    // =============================================================================

    // Hair Analysis & AI Features - Updated for pink-purple palette
    brushPurple: beautyColors['razzle-dazzle-rose'][500], // Razzle-dazzle-rose for brush tools
    sparkleYellow: semanticColors.warning, // Consistent amber for highlights (kept)
    zonePrimary: beautyColors['razzle-dazzle-rose'][500], // Primary analysis zones - razzle-dazzle-rose
    zoneSecondary: beautyColors.sage[500], // Secondary analysis zones (kept teal for trust)

    // Hair Zones and Analysis (professional beauty context) - Pink-purple harmony
    hairZoneRoot: beautyColors.sage[500], // Root zone in professional teal (kept for trust)
    hairZoneMid: beautyColors['razzle-dazzle-rose'][500], // Mid-length in razzle-dazzle-rose
    hairZoneEnd: beautyColors['salmon-pink'][500], // Ends in salmon-pink
  },
};
