/**
 * Personalized AI Hook - Enhanced Context-Aware AI Integration
 *
 * This hook provides enhanced AI capabilities with salon-specific personalization,
 * regional adaptations, and inventory-aware recommendations.
 *
 * Features:
 * - Automatic salon context injection
 * - Regional preference adaptation
 * - Cultural sensitivity handling
 * - Inventory-aware product filtering
 * - Performance-optimized caching
 */

import { useState, useCallback, useEffect, useMemo } from 'react';
import { useAuthStore } from '@/stores/auth-store';
import { usePersonalizationStore } from '@/stores/personalization-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { logger } from '@/utils/logger';
import { supabase } from '@/lib/supabase';

interface PersonalizedAIConfig {
  enableRegionalAdaptation?: boolean;
  enableInventoryFiltering?: boolean;
  enableCulturalSensitivity?: boolean;
  enablePerformanceOptimization?: boolean;
  cacheResults?: boolean;
}

interface PersonalizedAIRequest {
  type: 'formula' | 'diagnosis' | 'chat' | 'recommendation';
  data: any;
  config?: PersonalizedAIConfig;
}

interface PersonalizedAIResponse {
  result: any;
  confidence: number;
  personalizationApplied: {
    regional: boolean;
    inventory: boolean;
    cultural: boolean;
    performance: boolean;
  };
  contextUsed: {
    salonSpecific: boolean;
    brandPreferences: boolean;
    regionalData: boolean;
    culturalSensitivities: string[];
  };
  recommendations?: any[];
  metadata: {
    processingTime: number;
    cacheHit: boolean;
    model: string;
    tokens?: number;
    cost?: number;
  };
}

export const usePersonalizedAI = (config: PersonalizedAIConfig = {}) => {
  const { user } = useAuthStore();
  const {
    config: personalizationConfig,
    regionalData,
    isInitialized: personalizationInitialized,
    initialize: initializePersonalization,
    getEnhancedAIContext,
    getBrandFilteringRules,
    getRegionalAdaptations,
    getCulturalSensitivities,
    getPerformanceOptimizations,
  } = usePersonalizationStore();

  const { configuration: salonConfig } = useSalonConfigStore();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [cache, setCache] = useState<Map<string, { data: any; timestamp: number; ttl: number }>>(
    new Map()
  );

  // Default configuration
  const defaultConfig: PersonalizedAIConfig = {
    enableRegionalAdaptation: true,
    enableInventoryFiltering: true,
    enableCulturalSensitivity: true,
    enablePerformanceOptimization: true,
    cacheResults: true,
    ...config,
  };

  // Initialize personalization if needed
  useEffect(() => {
    if (user?.salonId && !personalizationInitialized) {
      initializePersonalization(user.salonId).catch(error => {
        logger.error('Failed to initialize personalization', 'usePersonalizedAI', error);
      });
    }
  }, [user?.salonId, personalizationInitialized, initializePersonalization]);

  // Generate cache key
  const generateCacheKey = useCallback(
    (request: PersonalizedAIRequest): string => {
      const key = JSON.stringify({
        type: request.type,
        data: request.data,
        salonId: user?.salonId,
        config: request.config || defaultConfig,
      });
      return Buffer.from(key).toString('base64');
    },
    [user?.salonId, defaultConfig]
  );

  // Check cache
  const getCachedResult = useCallback(
    (key: string, ttl: number = 10 * 60 * 1000): any => {
      const cached = cache.get(key);
      if (!cached) return null;

      if (Date.now() - cached.timestamp > ttl) {
        cache.delete(key);
        return null;
      }

      return cached.data;
    },
    [cache]
  );

  // Set cache
  const setCachedResult = useCallback((key: string, data: any, ttl: number = 10 * 60 * 1000) => {
    setCache(
      prev =>
        new Map(
          prev.set(key, {
            data,
            timestamp: Date.now(),
            ttl,
          })
        )
    );
  }, []);

  // Build enhanced context for AI requests
  const buildEnhancedContext = useCallback(() => {
    if (!personalizationConfig || !user?.salonId) {
      return null;
    }

    const baseContext = getEnhancedAIContext();
    const brandRules = getBrandFilteringRules();
    const regionalAdaptations = getRegionalAdaptations();
    const culturalSensitivities = getCulturalSensitivities();
    const performanceOptimizations = getPerformanceOptimizations();

    return {
      ...baseContext,
      brandFiltering: defaultConfig.enableInventoryFiltering ? brandRules : null,
      regional: defaultConfig.enableRegionalAdaptation ? regionalAdaptations : null,
      cultural: defaultConfig.enableCulturalSensitivity ? culturalSensitivities : [],
      performance: defaultConfig.enablePerformanceOptimization ? performanceOptimizations : null,
      salonConfiguration: salonConfig,
    };
  }, [
    personalizationConfig,
    user?.salonId,
    getEnhancedAIContext,
    getBrandFilteringRules,
    getRegionalAdaptations,
    getCulturalSensitivities,
    getPerformanceOptimizations,
    salonConfig,
    defaultConfig,
  ]);

  // Generate personalized formula
  const generatePersonalizedFormula = useCallback(
    async (
      diagnosis: any,
      desiredLook: any,
      availableProducts?: any[]
    ): Promise<PersonalizedAIResponse> => {
      const startTime = Date.now();
      setIsLoading(true);
      setError(null);

      try {
        const request: PersonalizedAIRequest = {
          type: 'formula',
          data: { diagnosis, desiredLook, availableProducts },
          config: defaultConfig,
        };

        const cacheKey = generateCacheKey(request);

        // Check cache
        if (defaultConfig.cacheResults) {
          const cached = getCachedResult(cacheKey);
          if (cached) {
            return {
              ...cached,
              metadata: {
                ...cached.metadata,
                cacheHit: true,
                processingTime: Date.now() - startTime,
              },
            };
          }
        }

        const enhancedContext = buildEnhancedContext();

        // Call Supabase Edge Function with enhanced context
        const { data, error } = await supabase.functions.invoke('salonier-assistant', {
          body: {
            action: 'generate_formula',
            salonId: user?.salonId,
            currentDiagnosis: diagnosis,
            desiredLook: desiredLook,
            availableProducts: availableProducts || [],
            enhancedContext,
            personalizationConfig: personalizationConfig,
          },
        });

        if (error) throw error;

        // Build response with personalization metadata
        const response: PersonalizedAIResponse = {
          result: data.formula,
          confidence: data.confidence || 0.85,
          personalizationApplied: {
            regional: !!(enhancedContext?.regional && defaultConfig.enableRegionalAdaptation),
            inventory: !!(
              enhancedContext?.brandFiltering && defaultConfig.enableInventoryFiltering
            ),
            cultural: !!(
              enhancedContext?.cultural?.length > 0 && defaultConfig.enableCulturalSensitivity
            ),
            performance: !!(
              enhancedContext?.performance && defaultConfig.enablePerformanceOptimization
            ),
          },
          contextUsed: {
            salonSpecific: !!enhancedContext?.salon,
            brandPreferences: !!(enhancedContext?.brandFiltering?.preferredBrands?.length > 0),
            regionalData: !!enhancedContext?.regional,
            culturalSensitivities: enhancedContext?.cultural || [],
          },
          recommendations: data.alternatives || [],
          metadata: {
            processingTime: Date.now() - startTime,
            cacheHit: false,
            model: data.model || 'gpt-4o',
            tokens: data.tokens,
            cost: data.cost,
          },
        };

        // Cache result
        if (defaultConfig.cacheResults) {
          setCachedResult(cacheKey, response);
        }

        return response;
      } catch (error) {
        setError(error as Error);
        logger.error('Failed to generate personalized formula', 'usePersonalizedAI', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [
      user?.salonId,
      personalizationConfig,
      defaultConfig,
      generateCacheKey,
      getCachedResult,
      setCachedResult,
      buildEnhancedContext,
    ]
  );

  // Analyze hair with personalization
  const analyzeHairWithPersonalization = useCallback(
    async (imageUrl: string, analysisType?: string): Promise<PersonalizedAIResponse> => {
      const startTime = Date.now();
      setIsLoading(true);
      setError(null);

      try {
        const request: PersonalizedAIRequest = {
          type: 'diagnosis',
          data: { imageUrl, analysisType },
          config: defaultConfig,
        };

        const cacheKey = generateCacheKey(request);

        // Check cache
        if (defaultConfig.cacheResults) {
          const cached = getCachedResult(cacheKey);
          if (cached) {
            return {
              ...cached,
              metadata: {
                ...cached.metadata,
                cacheHit: true,
                processingTime: Date.now() - startTime,
              },
            };
          }
        }

        const enhancedContext = buildEnhancedContext();

        // Call Supabase Edge Function
        const { data, error } = await supabase.functions.invoke('salonier-assistant', {
          body: {
            action: 'analyze_hair',
            salonId: user?.salonId,
            imageUrl,
            analysisType: analysisType || 'comprehensive',
            enhancedContext,
            personalizationConfig: personalizationConfig,
          },
        });

        if (error) throw error;

        const response: PersonalizedAIResponse = {
          result: data.analysis,
          confidence: data.confidence || 0.9,
          personalizationApplied: {
            regional: !!(enhancedContext?.regional && defaultConfig.enableRegionalAdaptation),
            inventory: false, // Diagnosis doesn't use inventory
            cultural: !!(
              enhancedContext?.cultural?.length > 0 && defaultConfig.enableCulturalSensitivity
            ),
            performance: !!(
              enhancedContext?.performance && defaultConfig.enablePerformanceOptimization
            ),
          },
          contextUsed: {
            salonSpecific: !!enhancedContext?.salon,
            brandPreferences: false,
            regionalData: !!enhancedContext?.regional,
            culturalSensitivities: enhancedContext?.cultural || [],
          },
          metadata: {
            processingTime: Date.now() - startTime,
            cacheHit: false,
            model: data.model || 'gpt-4o',
            tokens: data.tokens,
            cost: data.cost,
          },
        };

        // Cache result
        if (defaultConfig.cacheResults) {
          setCachedResult(cacheKey, response, 5 * 60 * 1000); // 5 minutes for analysis
        }

        return response;
      } catch (error) {
        setError(error as Error);
        logger.error('Failed to analyze hair with personalization', 'usePersonalizedAI', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [
      user?.salonId,
      personalizationConfig,
      defaultConfig,
      generateCacheKey,
      getCachedResult,
      setCachedResult,
      buildEnhancedContext,
    ]
  );

  // Chat with personalized context
  const chatWithPersonalization = useCallback(
    async (
      message: string,
      conversationHistory?: any[],
      context?: any
    ): Promise<PersonalizedAIResponse> => {
      const startTime = Date.now();
      setIsLoading(true);
      setError(null);

      try {
        const enhancedContext = buildEnhancedContext();

        // Call Supabase Edge Function
        const { data, error } = await supabase.functions.invoke('salonier-assistant', {
          body: {
            action: 'chat_assistant',
            salonId: user?.salonId,
            userId: user?.id,
            message,
            conversationHistory: conversationHistory || [],
            context: {
              ...context,
              enhanced: enhancedContext,
            },
            personalizationConfig: personalizationConfig,
          },
        });

        if (error) throw error;

        const response: PersonalizedAIResponse = {
          result: {
            message: data.response,
            suggestions: data.suggestions || [],
            actions: data.actions || [],
          },
          confidence: data.confidence || 0.8,
          personalizationApplied: {
            regional: !!(enhancedContext?.regional && defaultConfig.enableRegionalAdaptation),
            inventory: !!(
              enhancedContext?.brandFiltering && defaultConfig.enableInventoryFiltering
            ),
            cultural: !!(
              enhancedContext?.cultural?.length > 0 && defaultConfig.enableCulturalSensitivity
            ),
            performance: !!(
              enhancedContext?.performance && defaultConfig.enablePerformanceOptimization
            ),
          },
          contextUsed: {
            salonSpecific: !!enhancedContext?.salon,
            brandPreferences: !!(enhancedContext?.brandFiltering?.preferredBrands?.length > 0),
            regionalData: !!enhancedContext?.regional,
            culturalSensitivities: enhancedContext?.cultural || [],
          },
          metadata: {
            processingTime: Date.now() - startTime,
            cacheHit: false,
            model: data.model || 'gpt-4o-mini',
            tokens: data.tokens,
            cost: data.cost,
          },
        };

        return response;
      } catch (error) {
        setError(error as Error);
        logger.error('Failed to chat with personalization', 'usePersonalizedAI', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [user?.salonId, user?.id, personalizationConfig, defaultConfig, buildEnhancedContext]
  );

  // Get personalized recommendations
  const getPersonalizedRecommendations = useCallback(
    async (
      context: any,
      type: 'products' | 'techniques' | 'brands' = 'products'
    ): Promise<PersonalizedAIResponse> => {
      const startTime = Date.now();
      setIsLoading(true);
      setError(null);

      try {
        const enhancedContext = buildEnhancedContext();

        // This would integrate with the personalization service
        const recommendations = await Promise.resolve([
          {
            id: 'rec-1',
            type,
            title: 'Personalized Recommendation',
            confidence: 0.8,
            reasoning: ['Regional preference', 'Inventory available'],
            data: {},
          },
        ]);

        const response: PersonalizedAIResponse = {
          result: recommendations,
          confidence: 0.8,
          personalizationApplied: {
            regional: true,
            inventory: true,
            cultural: true,
            performance: true,
          },
          contextUsed: {
            salonSpecific: !!enhancedContext?.salon,
            brandPreferences: true,
            regionalData: !!enhancedContext?.regional,
            culturalSensitivities: enhancedContext?.cultural || [],
          },
          metadata: {
            processingTime: Date.now() - startTime,
            cacheHit: false,
            model: 'personalization-engine',
          },
        };

        return response;
      } catch (error) {
        setError(error as Error);
        logger.error('Failed to get personalized recommendations', 'usePersonalizedAI', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [buildEnhancedContext]
  );

  // Clear cache
  const clearCache = useCallback(() => {
    setCache(new Map());
  }, []);

  // Get personalization status
  const personalizationStatus = useMemo(
    () => ({
      initialized: personalizationInitialized,
      hasRegionalData: !!regionalData,
      hasBrandPreferences: !!(personalizationConfig?.availableBrands?.length > 0),
      hasCulturalSettings: getCulturalSensitivities().length > 0,
      featuresEnabled: {
        regional: defaultConfig.enableRegionalAdaptation,
        inventory: defaultConfig.enableInventoryFiltering,
        cultural: defaultConfig.enableCulturalSensitivity,
        performance: defaultConfig.enablePerformanceOptimization,
      },
    }),
    [
      personalizationInitialized,
      regionalData,
      personalizationConfig,
      getCulturalSensitivities,
      defaultConfig,
    ]
  );

  return {
    // State
    isLoading,
    error,
    personalizationStatus,

    // Actions
    generatePersonalizedFormula,
    analyzeHairWithPersonalization,
    chatWithPersonalization,
    getPersonalizedRecommendations,
    clearCache,

    // Utilities
    buildEnhancedContext,
    getCachedResult,
  };
};

export type PersonalizedAIHook = ReturnType<typeof usePersonalizedAI>;
