/**
 * AI Analysis Store - Optimizado v2
 *
 * Optimizaciones:
 * - performImageAnalysis unificada (-200 líneas)
 * - Logging condicional (sin logs en producción)
 * - ImageProcessor para compresión centralizada
 * - Retry con exponential backoff mejorado
 * - Reducido de 646 a ~390 líneas
 */

import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, getCurrentSalonId } from '@/lib/supabase';
import {
  FunctionsHttpError,
  FunctionsRelayError,
  FunctionsFetchError,
} from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import { ImageProcessor } from '@/utils/image-processor';
import { uploadAndAnonymizeImage } from '@/utils/secure-image-upload';
import { useAuthStore } from '@/stores/auth-store';
import { refreshUrlIfNeeded } from '@/utils/signed-url-manager';
import { HairZone } from '@/types/hair-diagnosis';
import * as Haptics from 'expo-haptics';

// ============= INTERFACES =============
interface ZoneAnalysisResult {
  zone: string;
  level: number;
  depthLevel?: number; // Compatibilidad
  tone: string;
  reflect: string;
  undertone?: string; // Compatibilidad
  state: string;
  grayPercentage?: number;
  porosity: string;
  elasticity: string;
  resistance: string;
  damage: string;
  unwantedTone?: string;
  confidence: number;
  grayType?: string;
  grayPattern?: string;
  pigmentAccumulation?: string;
  cuticleState?: string;
  demarkationBands?: { location: number; contrast: string }[];
}

interface AIAnalysisResult {
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallReflect: string;
  overallUndertone?: string; // Compatibilidad
  averageLevel: number;
  averageDepthLevel?: number; // Compatibilidad
  zoneAnalysis: {
    roots: ZoneAnalysisResult;
    mids: ZoneAnalysisResult;
    ends: ZoneAnalysisResult;
  };
  detectedChemicalProcess?: string;
  estimatedLastProcessDate?: string;
  detectedHomeRemedies?: boolean;
  detectedRisks?: {
    metalSalts: { detected: boolean; confidence: number; signs: string[] };
    henna: { detected: boolean; confidence: number; signs: string[] };
    extremeDamage: { detected: boolean; zones: string[] };
  };
  serviceComplexity?: 'simple' | 'medium' | 'complex';
  estimatedTime?: number;
  overallCondition: string;
  recommendations: string[];
  overallConfidence: number;
  analysisTimestamp: number;
}

interface DesiredPhotoAnalysis {
  photoId: string;
  detectedLevel: number;
  detectedTone: string;
  detectedTechnique: string;
  detectedTones: string[];
  viabilityScore: number;
  estimatedSessions: number;
  requiredProcesses: string[];
  confidence: number;
  warnings?: string[];
  zoneAnalysis?: {
    [zoneName: string]: {
      level: number;
      tone: string;
      confidence: number;
      [key: string]: unknown;
    };
  }; // Zone-specific analysis data
}

interface AIAnalysisSettings {
  autoFaceBlur: boolean;
  imageQualityThreshold: number;
  privacyMode: boolean;
  saveAnalysisHistory: boolean;
}

interface AIAnalysisState {
  isAnalyzing: boolean;
  analysisResult: AIAnalysisResult | null;
  analysisHistory: AIAnalysisResult[];
  isAnalyzingDesiredPhoto: boolean;
  analyzingPhotoId: string | null;
  desiredPhotoAnalyses: Record<string, DesiredPhotoAnalysis>;
  settings: AIAnalysisSettings;
  privacyMode: boolean;
  // ADD: Cancellation support
  currentAnalysisController: AbortController | null;

  analyzeImage: (imageUri: string) => Promise<void>;
  analyzeDesiredPhoto: (
    photoId: string,
    imageUri: string,
    diagnosis: AIAnalysisResult | number | Record<string, unknown>,
    clientId?: string
  ) => Promise<DesiredPhotoAnalysis | null>;
  clearAnalysis: () => void;
  updateSettings: (settings: Partial<AIAnalysisSettings>) => Promise<void>;
  setPrivacyMode: (enabled: boolean) => void;
  getAnalysisHistory: () => AIAnalysisResult[];
  clearAnalysisHistory: () => Promise<void>;
}

// ============= CONFIGURACIÓN =============
const RETRY_CONFIG = {
  maxAttempts: 3,
  timeout: 30000,
  backoffDelay: 1500,
};

// ============= HELPER FUNCTIONS =============

/**
 * Refresh signed URL if it's expired or close to expiring
 */
async function ensureValidSignedUrl(imageUrl: string): Promise<string> {
  try {
    // Check if it's a signed URL (contains signature and expires parameters)
    const url = new URL(imageUrl);
    const token = url.searchParams.get('token');
    const expires = url.searchParams.get('expires');

    if (!token || !expires) {
      // Not a signed URL, return as-is
      return imageUrl;
    }

    // Check if URL is close to expiring (within 5 minutes)
    const expiresTimestamp = parseInt(expires) * 1000; // Convert to milliseconds
    const now = Date.now();
    const timeUntilExpiry = expiresTimestamp - now;
    const fiveMinutes = 5 * 60 * 1000;

    if (timeUntilExpiry <= fiveMinutes) {
      logger.info('Signed URL needs refresh', 'AIAnalysisStore', {
        expiresAt: expiresTimestamp,
        timeUntilExpiry,
      });

      // Extract bucket and path from URL
      const pathMatch = url.pathname.match(/\/storage\/v1\/object\/sign\/([^\/]+)\/(.+)/);
      if (pathMatch && pathMatch[1] && pathMatch[2]) {
        const bucket = pathMatch[1];
        const path = pathMatch[2];

        const refreshedUrl = await refreshUrlIfNeeded(bucket, path, imageUrl);
        if (refreshedUrl) {
          logger.info('Successfully refreshed signed URL', 'AIAnalysisStore');
          return refreshedUrl;
        }
      }

      logger.warn('Could not refresh signed URL, using original', 'AIAnalysisStore');
    }

    return imageUrl;
  } catch (error: unknown) {
    logger.error('Error checking/refreshing signed URL', 'AIAnalysisStore', error);
    return imageUrl; // Return original URL on error
  }
}

// ============= FUNCIÓN COMPARTIDA PRINCIPAL =============
async function performImageAnalysis(
  imageUri: string,
  task: 'diagnose_image' | 'analyze_desired_look',
  additionalPayload: Record<string, unknown> = {}
): Promise<Record<string, unknown>> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), RETRY_CONFIG.timeout);

  try {
    // Determinar si es una URL (privada firmada o pública) o una URI local
    const isPublicUrl = imageUri.startsWith('http://') || imageUri.startsWith('https://');
    const payload: Record<string, unknown> = { ...additionalPayload };

    if (isPublicUrl) {
      // Si es una URL firmada privada, refrescar si es necesario
      logger.info('Using URL for analysis', 'AIAnalysisStore', {
        url: imageUri.substring(0, 100) + '...',
      });
      const validUrl = await ensureValidSignedUrl(imageUri);
      payload.imageUrl = validUrl;

      // Also include base64 as fallback for better reliability
      try {
        const purpose = task === 'diagnose_image' ? 'diagnosis' : 'desired';
        const fallbackBase64 = await ImageProcessor.compressForAI(imageUri, purpose);
        if (fallbackBase64 && fallbackBase64.length > 4000) {
          payload.imageBase64 = fallbackBase64;
          logger.debug('Added base64 fallback for URL analysis', 'AIAnalysisStore', {
            base64Length: fallbackBase64.length,
          });
        } else {
          logger.debug('Skipping base64 fallback: too small or invalid', 'AIAnalysisStore', {
            base64Length: fallbackBase64?.length || 0,
          });
        }
      } catch (error) {
        logger.warn('Could not create base64 fallback, using URL only', 'AIAnalysisStore', error);
      }
    } else {
      // Si es una URI local, mantener compatibilidad con base64 por ahora
      // TODO: Migrar completamente a URLs después de actualizar el flujo de captura
      logger.info('Using base64 for compatibility (local URI)', 'AIAnalysisStore');

      // Validar y comprimir imagen
      const validation = await ImageProcessor.validateQuality(imageUri);
      if (!validation.valid) {
        const errors = validation.issues
          .filter(i => i.severity === 'error')
          .map(i => i.message)
          .join('. ');
        throw new Error(errors || 'Imagen no válida');
      }

      const purpose = task === 'diagnose_image' ? 'diagnosis' : 'desired';
      const imageBase64 = await ImageProcessor.compressForAI(imageUri, purpose);
      payload.imageBase64 = imageBase64;
    }

    // 2. Verificar sesión y obtener token
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('No hay sesión activa. Por favor, cierra y vuelve a abrir la aplicación.');
    }

    // 2.5. Verificar que el usuario tenga salon_id
    const salonId = await getCurrentSalonId();
    if (!salonId) {
      logger.error('User has no salon_id, attempting to repair...', 'AIAnalysisStore');

      // Intentar reparar el perfil
      const authStore = useAuthStore.getState();
      if (authStore.user) {
        await authStore.initializeAuth(); // This will trigger ensureUserHasSalonId

        // Verificar nuevamente
        const repairedSalonId = await getCurrentSalonId();
        if (!repairedSalonId) {
          throw new Error(
            'Usuario no asociado a ningún salón. Por favor, contacta al administrador.'
          );
        }
      } else {
        throw new Error(
          'Usuario no autenticado correctamente. Por favor, cierra sesión y vuelve a iniciar.'
        );
      }
    }

    // 3. Llamar Edge Function con reintentos
    let lastError: Error | unknown;
    for (let attempt = 1; attempt <= RETRY_CONFIG.maxAttempts; attempt++) {
      try {
        // Debug: Log task being sent to Edge Function
        logger.debug('Calling Edge Function', 'AIAnalysisStore', {
          task,
          attempt,
          hasImageUrl: !!payload.imageUrl,
          hasImageBase64: !!payload.imageBase64,
        });

        // Debug payload before sending
        logger.debug('Sending payload to Edge Function', 'AIAnalysisStore', {
          task,
          payloadKeys: Object.keys(payload),
          hasImageUrl: !!payload.imageUrl,
          hasImageBase64: !!payload.imageBase64,
          imageUrlLength: payload.imageUrl ? String(payload.imageUrl).length : 0,
          imageBase64Length: payload.imageBase64 ? String(payload.imageBase64).length : 0,
        });

        // eslint-disable-next-line no-await-in-loop -- Sequential retries required for error recovery with delay
        const { data, error } = await supabase.functions.invoke('diagnose-image-hotfix', {
          body: { task, payload },
          headers: {
            Authorization: `Bearer ${session.access_token}`,
          },
          signal: controller.signal,
        });

        // Log detailed response information for debugging
        logger.debug('Edge function response', 'AIAnalysisStore', {
          attempt,
          task,
          hasError: !!error,
          hasData: !!data,
          dataKeys: data ? Object.keys(data) : null,
          dataSuccess: data?.success,
          dataError: data?.error,
          resultPreview: data?.data
            ? {
                averageLevel: data.data.averageLevel,
                overallTone: data.data.overallTone,
                hasZoneAnalysis: !!data.data.zoneAnalysis,
                zoneCount: data.data.zoneAnalysis ? Object.keys(data.data.zoneAnalysis).length : 0,
              }
            : null,
        });

        if (error) {
          logger.error('Edge function HTTP error', 'AIAnalysisStore', error);
          // eslint-disable-next-line no-await-in-loop -- Sequential retries required for error recovery with delay
          const errorMessage = await handleEdgeFunctionError(error);
          // Si es error 401, intentar refrescar sesión
          if (errorMessage.includes('401') || errorMessage.includes('Invalid token')) {
            logger.warn('Token inválido, intentando refrescar sesión...', 'AIAnalysisStore');
            const {
              data: { session: newSession },
              error: refreshError,
              // eslint-disable-next-line no-await-in-loop -- Sequential retries required for error recovery with delay
            } = await supabase.auth.refreshSession();
            if (refreshError || !newSession) {
              throw new Error('Sesión expirada. Por favor, cierra y vuelve a abrir la aplicación.');
            }
            // Reintentar con el nuevo token
            continue;
          }
          throw new Error(errorMessage);
        }
        if (!data) throw new Error('Sin respuesta del servidor');
        if (data.success === true && data.data === null)
          throw new Error('Respuesta vacía de OpenAI');

        if (!data.success) {
          logger.error('Edge function returned error', 'AIAnalysisStore', {
            success: data.success,
            error: data.error,
            attempt,
            maxAttempts: RETRY_CONFIG.maxAttempts,
            task,
          });

          if (
            data.error &&
            data.error.includes('Invalid response from OpenAI') &&
            attempt < RETRY_CONFIG.maxAttempts
          ) {
            logger.warn(
              `Retrying due to OpenAI response error (attempt ${attempt}/${RETRY_CONFIG.maxAttempts})...`,
              'AIAnalysisStore'
            );
            // eslint-disable-next-line no-await-in-loop -- Sequential retries required for error recovery with delay
            await new Promise(r => setTimeout(r, RETRY_CONFIG.backoffDelay * attempt));
            continue;
          }
          throw new Error(data.error || 'Error desconocido');
        }

        if (!data.data) throw new Error('Respuesta incompleta');

        clearTimeout(timeoutId);
        return data.data;
      } catch (error: unknown) {
        lastError = error;
        const errorAsError = error as Error;
        if (errorAsError.name === 'AbortError') throw new Error('TIMEOUT_ERROR');

        if (
          attempt < RETRY_CONFIG.maxAttempts &&
          !errorAsError.message?.includes('TIMEOUT_ERROR')
        ) {
          const delay = RETRY_CONFIG.backoffDelay * Math.pow(2, attempt - 1);
          // eslint-disable-next-line no-await-in-loop -- Sequential retries required for error recovery with delay
          await new Promise(r => setTimeout(r, delay));
        } else {
          throw error;
        }
      }
    }
    throw lastError || new Error('Máximos reintentos alcanzados');
  } catch (error: unknown) {
    clearTimeout(timeoutId);
    throw error;
  } finally {
    clearTimeout(timeoutId);
  }
}

// ============= FUNCIONES ESPECÍFICAS =============
async function performAIAnalysis(imageUri: string): Promise<AIAnalysisResult> {
  // Debug: Performing AI analysis
  logger.debug('Performing AI analysis', 'AIAnalysisStore', { task: 'diagnose_image' });

  const result = await performImageAnalysis(imageUri, 'diagnose_image', {});

  // Validación más flexible - solo requerir campos esenciales
  if (!result.averageLevel && !result.level && !result.averageDepthLevel) {
    logger.warn('Analysis missing level information, using defaults', 'AIAnalysisStore');
    result.averageLevel = 6; // Default level
  }

  // Agregar campos opcionales con valores por defecto si no existen
  if (!result.hairThickness) {
    result.hairThickness = 'Medio';
  }
  if (!result.hairDensity) {
    result.hairDensity = 'Media';
  }
  if (!result.zoneAnalysis) {
    // Crear estructura básica si no existe
    result.zoneAnalysis = {
      [HairZone.ROOTS]: { level: result.averageLevel || 6 },
      [HairZone.MIDS]: { level: result.averageLevel || 6 },
      [HairZone.ENDS]: { level: result.averageLevel || 6 },
    };
  }

  return { ...result, analysisTimestamp: Date.now() } as AIAnalysisResult;
}

async function analyzeDesiredColorPhoto(
  photoId: string,
  imageUri: string,
  diagnosis: AIAnalysisResult | number | Record<string, unknown>
): Promise<DesiredPhotoAnalysis> {
  // Debug: Analyzing desired color photo
  logger.debug('Analyzing desired color photo', 'AIAnalysisStore', {
    task: 'analyze_desired_look',
    photoId,
  });

  // Si diagnosis es un número, mantener compatibilidad hacia atrás
  const payload = typeof diagnosis === 'number' ? { currentLevel: diagnosis } : { diagnosis };

  const result = await performImageAnalysis(imageUri, 'analyze_desired_look', payload);
  return { photoId, ...result } as DesiredPhotoAnalysis;
}

// ============= MANEJO DE ERRORES =============
async function handleEdgeFunctionError(error: unknown): Promise<string> {
  logger.error('Edge Function Error', 'AIAnalysisStore', error);

  if (error instanceof FunctionsHttpError) {
    const status = error.context.status;
    logger.error('HTTP Status', 'AIAnalysisStore', status);

    try {
      const errorData = await error.context.json();
      logger.error('Function returned error', 'AIAnalysisStore', errorData);

      // Incluir el status HTTP en el mensaje para mejor manejo
      if (status === 401) {
        return `HTTP 401: ${errorData.error || errorData.message || 'Invalid token'}`;
      }

      return errorData.error || errorData.message || 'Error en el servidor';
    } catch (e) {
      logger.error('Could not parse error response', 'AIAnalysisStore', e);
      return `HTTP ${status}: Error al procesar la respuesta del servidor`;
    }
  } else if (error instanceof FunctionsRelayError) {
    return 'Error de conexión con el servidor';
  } else if (error instanceof FunctionsFetchError) {
    return 'Error al conectar con el servidor';
  } else {
    const errorAsError = error as Error;
    return errorAsError.message || 'Error desconocido';
  }
}

// ============= ZUSTAND STORE =============
export const useAIAnalysisStore = create<AIAnalysisState>((set, get) => ({
  isAnalyzing: false,
  analysisResult: null,
  analysisHistory: [],
  isAnalyzingDesiredPhoto: false,
  analyzingPhotoId: null,
  desiredPhotoAnalyses: {},
  privacyMode: true,
  currentAnalysisController: null,
  settings: {
    autoFaceBlur: true,
    imageQualityThreshold: 60,
    privacyMode: true,
    saveAnalysisHistory: false,
  },

  analyzeImage: async (imageUri: string) => {
    // SAFETY: Cancel any ongoing analysis to prevent race conditions
    const currentController = get().currentAnalysisController;
    if (currentController) {
      currentController.abort();
      logger.warn('Cancelled previous analysis to start new one', 'AIAnalysisStore');
    }

    // Create new abort controller for this analysis
    const newController = new AbortController();
    set({
      isAnalyzing: true,
      analysisResult: null,
      currentAnalysisController: newController,
    });

    try {
      // Debug: Starting image analysis
      logger.debug('Starting image analysis', 'AIAnalysisStore', { task: 'diagnose_image' });

      const result = await performAIAnalysis(imageUri);
      logger.info('AI Analysis completed successfully', 'AIAnalysisStore', {
        hairAnalysis: {
          averageLevel: result.averageLevel,
          overallTone: result.overallTone,
          overallReflect: result.overallReflect,
          hairThickness: result.hairThickness,
          hairDensity: result.hairDensity,
          overallCondition: result.overallCondition,
        },
        zoneAnalysis: {
          roots: {
            level: result.zoneAnalysis?.roots?.level,
            tone: result.zoneAnalysis?.roots?.tone,
            reflect: result.zoneAnalysis?.roots?.reflect,
            confidence: result.zoneAnalysis?.roots?.confidence,
          },
          mids: {
            level: result.zoneAnalysis?.mids?.level,
            tone: result.zoneAnalysis?.mids?.tone,
            reflect: result.zoneAnalysis?.mids?.reflect,
            confidence: result.zoneAnalysis?.mids?.confidence,
          },
          ends: {
            level: result.zoneAnalysis?.ends?.level,
            tone: result.zoneAnalysis?.ends?.tone,
            reflect: result.zoneAnalysis?.ends?.reflect,
            confidence: result.zoneAnalysis?.ends?.confidence,
          },
        },
        detectedProcesses: {
          chemicalProcess: result.detectedChemicalProcess,
          lastProcessDate: result.estimatedLastProcessDate,
          homeRemedies: result.detectedHomeRemedies,
        },
        riskDetection: result.detectedRisks,
        recommendations: result.recommendations?.slice(0, 3),
        overallConfidence: result.overallConfidence,
        serviceComplexity: result.serviceComplexity,
        estimatedTime: result.estimatedTime,
        analysisTimestamp: result.analysisTimestamp,
      });

      // SAFETY: Only update state if this analysis wasn't cancelled
      const currentState = get();
      if (currentState.currentAnalysisController === newController) {
        set(state => ({
          isAnalyzing: false,
          analysisResult: result,
          currentAnalysisController: null,
          analysisHistory: state.settings.saveAnalysisHistory
            ? [...state.analysisHistory, result]
            : state.analysisHistory,
        }));
      } else {
        // Analysis was cancelled, don't update state
        logger.info(
          'Analysis completed but was cancelled, skipping state update',
          'AIAnalysisStore'
        );
        return;
      }

      // Haptic feedback for successful analysis
      try {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } catch {
        // Silently fail if haptics not available
      }

      // Guardar historial si está habilitado
      const { settings, analysisHistory } = get();
      if (settings.saveAnalysisHistory) {
        try {
          await AsyncStorage.setItem('salonier-analysis-history', JSON.stringify(analysisHistory));
        } catch (error) {
          logger.error('Error saving analysis history', 'AIAnalysisStore', error);
        }
      }
    } catch (error: unknown) {
      // SAFETY: Only update error state if analysis wasn't cancelled
      const currentState = get();
      if (currentState.currentAnalysisController === newController) {
        set({
          isAnalyzing: false,
          currentAnalysisController: null,
        });
      }
      logger.error('Error in analyzeImage', 'AIAnalysisStore', error);
      const errorAsError = error as Error;

      // Mensajes de error user-friendly
      if (errorAsError.message === 'TIMEOUT_ERROR') {
        throw new Error('El análisis tardó demasiado. Por favor, intenta de nuevo.');
      } else if (errorAsError.message?.includes('servidor')) {
        throw new Error('No se pudo conectar con el servidor. Verifica tu conexión.');
      } else {
        throw new Error('Error al procesar la imagen. Por favor, intenta con otra foto.');
      }
    }
  },

  analyzeDesiredPhoto: async (
    photoId: string,
    imageUri: string,
    diagnosis: AIAnalysisResult | number | Record<string, unknown>,
    clientId?: string
  ) => {
    logger.info('Starting desired photo analysis', 'AIAnalysisStore', {
      photoId,
      hasDiagnosis: !!diagnosis,
    });
    set({ isAnalyzingDesiredPhoto: true, analyzingPhotoId: photoId });

    try {
      let finalImageUri = imageUri;

      // Check if image is already a URL (could be private signed URL)
      const isPublicUrl = imageUri.startsWith('http://') || imageUri.startsWith('https://');

      if (!isPublicUrl && clientId) {
        // Upload and anonymize the image first
        logger.info('Uploading desired photo for secure analysis', 'AIAnalysisStore');
        const uploadResult = await uploadAndAnonymizeImage(imageUri, {
          clientId,
          photoType: 'desired',
          onProgress: progress => {
            logger.debug(`Desired photo upload progress: ${progress}%`, 'AIAnalysisStore');
          },
        });

        if (!uploadResult.success || !uploadResult.publicUrl) {
          throw new Error(uploadResult.error || 'Failed to upload desired photo');
        }

        finalImageUri = uploadResult.publicUrl;
        logger.info('Desired photo uploaded successfully', 'AIAnalysisStore', {
          privateSignedUrl: finalImageUri,
          bucket: 'client-photos-private',
          expiresIn: '1 hour',
          requiresAuth: true,
        });
      }

      const analysis = await analyzeDesiredColorPhoto(photoId, finalImageUri, diagnosis);

      set(state => ({
        isAnalyzingDesiredPhoto: false,
        analyzingPhotoId: null,
        desiredPhotoAnalyses: {
          ...state.desiredPhotoAnalyses,
          [photoId]: analysis,
        },
      }));

      return analysis;
    } catch (error: unknown) {
      set({ isAnalyzingDesiredPhoto: false, analyzingPhotoId: null });
      logger.error('Error analyzing desired photo', 'AIAnalysisStore', error);
      const errorAsError = error as Error;

      if (errorAsError.message === 'TIMEOUT_ERROR') {
        throw new Error('TIMEOUT_ERROR');
      }

      return null;
    }
  },

  clearAnalysis: () => {
    // SAFETY: Cancel any ongoing analysis to prevent crashes
    const currentState = get();
    if (currentState.currentAnalysisController) {
      currentState.currentAnalysisController.abort();
      logger.info('Cancelled ongoing analysis during clearAnalysis', 'AIAnalysisStore');
    }

    set({
      analysisResult: null,
      desiredPhotoAnalyses: {},
      analyzingPhotoId: null,
      currentAnalysisController: null,
      isAnalyzing: false,
      isAnalyzingDesiredPhoto: false,
    });
  },

  updateSettings: async (newSettings: Partial<AIAnalysisSettings>) => {
    const updatedSettings = { ...get().settings, ...newSettings };
    set({ settings: updatedSettings });

    try {
      await AsyncStorage.setItem('salonier-ai-settings', JSON.stringify(updatedSettings));
    } catch (error) {
      logger.error('Error saving AI settings', 'AIAnalysisStore', error);
    }
  },

  setPrivacyMode: (enabled: boolean) => {
    set({ privacyMode: enabled });
  },

  getAnalysisHistory: () => {
    return get().analysisHistory;
  },

  clearAnalysisHistory: async () => {
    set({ analysisHistory: [] });
    try {
      await AsyncStorage.removeItem('salonier-analysis-history');
    } catch (error) {
      logger.error('Error clearing analysis history', 'AIAnalysisStore', error);
    }
  },
}));

// ============= INICIALIZACIÓN =============
const initializeAIAnalysisStore = async () => {
  try {
    const savedSettings = await AsyncStorage.getItem('salonier-ai-settings');
    const savedHistory = await AsyncStorage.getItem('salonier-analysis-history');

    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      useAIAnalysisStore.getState().updateSettings(settings);
    }

    if (savedHistory) {
      const history = JSON.parse(savedHistory);
      useAIAnalysisStore.setState({ analysisHistory: history });
    }
  } catch (error) {
    logger.error('Error initializing AI analysis store', 'AIAnalysisStore', error);
  }
};

initializeAIAnalysisStore();
