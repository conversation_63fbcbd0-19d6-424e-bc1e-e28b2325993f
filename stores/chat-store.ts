import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import { useAuthStore } from './auth-store';
import { decode } from 'base64-arraybuffer';

export interface ChatAttachment {
  id?: string;
  type: 'image' | 'document';
  url: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  width?: number;
  height?: number;
  thumbnailUrl?: string;
  localUri?: string; // Para imágenes no subidas aún
  uploadStatus?: 'pending' | 'uploading' | 'completed' | 'failed';
}

export interface ChatMessage {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
  costUsd?: number;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  localId?: string; // Para mensajes offline
  synced?: boolean;
  hasAttachments?: boolean;
  attachments?: ChatAttachment[];
}

export interface ChatConversation {
  id: string;
  salonId: string;
  userId: string;
  title: string;
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  status: 'active' | 'archived';
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
  // Campos calculados
  messageCount?: number;
  lastMessageAt?: Date;
  totalTokensUsed?: number;
  totalCostUsd?: number;
  // Nuevos campos
  isFavorite?: boolean;
  lastMessage?: string;
}

export interface ChatContextReference {
  id: string;
  messageId: string;
  referenceType: 'client' | 'service' | 'formula' | 'product' | 'image';
  referenceId: string;
  referenceData?: Record<string, unknown>;
  createdAt: Date;
}

interface ChatStore {
  // Estado
  conversations: ChatConversation[];
  messages: Record<string, ChatMessage[]>; // Indexed by conversationId
  activeConversationId: string | null;
  isLoading: boolean;
  isSending: boolean;
  error: Error | null;
  uploadingImages: Record<string, boolean>; // Track upload status by localId

  // Streaming state
  streamingMessage: {
    conversationId: string;
    messageId: string;
    content: string;
    isComplete: boolean;
    currentIndex?: number;
  } | null;
  typingStatus: 'idle' | 'thinking' | 'analyzing' | 'writing';
  streamingIntervalId: number | null;

  // Sincronización
  pendingSyncMessages: ChatMessage[];
  lastSync: Date | null;

  // Actions - Conversations
  loadConversations: () => Promise<void>;
  createConversation: (params: {
    title?: string;
    contextType?: ChatConversation['contextType'];
    contextId?: string;
    metadata?: Record<string, unknown>;
  }) => Promise<ChatConversation | null>;
  updateConversation: (id: string, updates: Partial<ChatConversation>) => Promise<void>;
  archiveConversation: (id: string) => Promise<void>;
  deleteConversation: (id: string) => Promise<void>;
  toggleFavorite: (id: string) => Promise<void>;
  setActiveConversation: (id: string | null) => void;

  // Actions - Messages
  loadMessages: (conversationId: string) => Promise<void>;
  sendMessage: (
    content: string,
    conversationId?: string,
    attachments?: ChatAttachment[]
  ) => Promise<void>;
  addOptimisticMessage: (message: Omit<ChatMessage, 'id' | 'createdAt'>) => string;
  updateOptimisticMessage: (localId: string, updates: Partial<ChatMessage>) => void;

  // Actions - Smart Titles
  generateIntelligentTitle: (
    conversationId: string,
    userMessage: string,
    assistantResponse: string
  ) => Promise<void>;

  // Actions - Attachments
  uploadChatImage: (imageUri: string, messageId?: string) => Promise<ChatAttachment | null>;
  updateAttachmentStatus: (
    messageId: string,
    attachmentIndex: number,
    status: ChatAttachment['uploadStatus']
  ) => void;

  // Actions - Context
  addContextReference: (
    messageId: string,
    reference: Omit<ChatContextReference, 'id' | 'messageId' | 'createdAt'>
  ) => Promise<void>;

  // Streaming actions
  startStreaming: (conversationId: string, messageId: string) => void;
  updateStreamingContent: (content: string, append?: boolean) => void;
  completeStreaming: () => void;
  setTypingStatus: (status: 'idle' | 'thinking' | 'analyzing' | 'writing') => void;

  // Sync
  syncPendingMessages: () => Promise<void>;

  // Memory management
  cleanupStreaming: () => void;
  cleanupConversationMemory: (excludeConversationId?: string) => void;
  scheduleMemoryCleanup: () => NodeJS.Timeout;

  // Utils
  generateSmartTitle: (content: string) => string;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  conversations: [],
  messages: {},
  activeConversationId: null,
  isLoading: false,
  isSending: false,
  error: null,
  uploadingImages: {},
  streamingMessage: null,
  typingStatus: 'idle' as const,
  streamingIntervalId: null,
  pendingSyncMessages: [],
  lastSync: null,
};

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Load conversations
      loadConversations: async () => {
        try {
          set({ isLoading: true, error: null });

          const { data: profile } = await supabase.auth.getUser();
          if (!profile?.user) {
            throw new Error('Usuario no autenticado');
          }

          // Use secure query with explicit salon_id filtering instead of unsafe view
          const { data, error } = await supabase
            .from('chat_conversations')
            .select(
              `
              *,
              chat_messages!inner (
                id,
                created_at,
                total_tokens,
                cost_usd
              )
            `
            )
            .eq('status', 'active')
            .order('updated_at', { ascending: false });

          if (error) throw error;

          const conversations = data.map((conv: Record<string, unknown>) => {
            // Calculate stats from related messages
            const messages = (conv.chat_messages as any[]) || [];
            const messageCount = messages.length;
            const lastMessageAt =
              messages.length > 0
                ? new Date(Math.max(...messages.map(m => new Date(m.created_at).getTime())))
                : undefined;
            const totalTokensUsed = messages.reduce((sum, m) => sum + (m.total_tokens || 0), 0);
            const totalCostUsd = messages.reduce(
              (sum, m) => sum + parseFloat(m.cost_usd || '0'),
              0
            );

            return {
              ...conv,
              createdAt: new Date(conv.created_at),
              updatedAt: new Date(conv.updated_at),
              lastMessageAt,
              messageCount,
              totalTokensUsed,
              totalCostUsd,
              isFavorite: conv.metadata?.isFavorite || false,
              lastMessage: conv.last_message || '',
              // Remove the nested messages from the final object
              chat_messages: undefined,
            };
          });

          set({ conversations, isLoading: false });
          logger.info('Chat conversations loaded', 'ChatStore', {
            count: conversations.length,
          });
        } catch (error) {
          logger.error('Error loading conversations', error);
          set({ error: error as Error, isLoading: false });
        }
      },

      // Create conversation
      createConversation: async params => {
        try {
          const { user } = useAuthStore.getState();

          if (!user?.id || !user?.salonId) {
            throw new Error('Usuario o salón no configurado');
          }

          const conversationData = {
            salon_id: user.salonId,
            user_id: user.id,
            title: params.title || 'Nueva conversación',
            context_type: params.contextType,
            context_id: params.contextId,
            metadata: params.metadata || {},
            status: 'active',
          };

          const { data, error } = await supabase
            .from('chat_conversations')
            .insert(conversationData)
            .select()
            .single();

          if (error) throw error;

          const newConversation: ChatConversation = {
            ...data,
            createdAt: new Date(data.created_at),
            updatedAt: new Date(data.updated_at),
            messageCount: 0,
            totalTokensUsed: 0,
            totalCostUsd: 0,
          };

          set(state => ({
            conversations: [newConversation, ...state.conversations],
            activeConversationId: newConversation.id,
          }));

          logger.info('Conversation created', 'ChatStore', {
            id: newConversation.id,
          });
          return newConversation;
        } catch (error) {
          logger.error('Error creating conversation', error);
          set({ error: error as Error });
          return null;
        }
      },

      // Update conversation (OPTIMISTIC UI)
      updateConversation: async (id, updates) => {
        // Store original for rollback
        const originalConversation = get().conversations.find(conv => conv.id === id);
        if (!originalConversation) {
          logger.warn('Conversation not found for update', 'ChatStore', { id });
          return;
        }

        // IMMEDIATE UI UPDATE
        set(state => ({
          conversations: state.conversations.map(conv =>
            conv.id === id ? { ...conv, ...updates, updatedAt: new Date() } : conv
          ),
        }));

        try {
          // BACKGROUND API CALL
          const { error } = await supabase.from('chat_conversations').update(updates).eq('id', id);

          if (error) throw error;

          logger.info('Conversation updated successfully', 'ChatStore', { id });
        } catch (error) {
          // ROLLBACK on error
          logger.error('Error updating conversation, rolling back', error);
          set(state => ({
            conversations: state.conversations.map(conv =>
              conv.id === id ? originalConversation : conv
            ),
            error: error as Error,
          }));
          throw error; // Re-throw for error handling in UI
        }
      },

      // Archive conversation
      archiveConversation: async id => {
        await get().updateConversation(id, { status: 'archived' });
        set(state => ({
          conversations: state.conversations.filter(conv => conv.id !== id),
          activeConversationId:
            state.activeConversationId === id ? null : state.activeConversationId,
        }));
      },

      // Delete conversation permanently (OPTIMISTIC UI)
      deleteConversation: async id => {
        const originalState = get();
        const conversationToDelete = originalState.conversations.find(conv => conv.id === id);
        const conversationMessages = originalState.messages[id];

        if (!conversationToDelete) {
          logger.warn('Conversation not found for deletion', 'ChatStore', { id });
          return;
        }

        // IMMEDIATE UI UPDATE (conversation disappears instantly)
        set(state => ({
          conversations: state.conversations.filter(conv => conv.id !== id),
          messages: Object.fromEntries(
            Object.entries(state.messages).filter(([key]) => key !== id)
          ),
          activeConversationId:
            state.activeConversationId === id ? null : state.activeConversationId,
        }));

        try {
          // BACKGROUND API CALL
          // Delete all messages first
          const { error: messagesError } = await supabase
            .from('chat_messages')
            .delete()
            .eq('conversation_id', id);

          if (messagesError) throw messagesError;

          // Delete the conversation
          const { error: conversationError } = await supabase
            .from('chat_conversations')
            .delete()
            .eq('id', id);

          if (conversationError) throw conversationError;

          logger.info('Conversation deleted permanently', 'ChatStore', { id });
        } catch (error) {
          // ROLLBACK on error (conversation reappears)
          logger.error('Error deleting conversation, rolling back', 'ChatStore', error);
          set(state => ({
            conversations: [conversationToDelete, ...state.conversations],
            messages: {
              ...state.messages,
              ...(conversationMessages && { [id]: conversationMessages }),
            },
            error: error as Error,
          }));
          throw error; // Re-throw for error handling in UI
        }
      },

      // Toggle favorite (OPTIMISTIC UI)
      toggleFavorite: async id => {
        const conversation = get().conversations.find(conv => conv.id === id);
        if (!conversation) return;

        const newFavoriteStatus = !conversation.isFavorite;

        // IMMEDIATE UI UPDATE
        set(state => ({
          conversations: state.conversations.map(conv =>
            conv.id === id ? { ...conv, isFavorite: newFavoriteStatus } : conv
          ),
        }));

        try {
          // BACKGROUND API CALL
          await get().updateConversation(id, {
            metadata: {
              ...conversation.metadata,
              isFavorite: newFavoriteStatus,
            },
          });
          logger.info('Favorite toggled successfully', 'ChatStore', {
            id,
            isFavorite: newFavoriteStatus,
          });
        } catch (error) {
          // ROLLBACK on error
          logger.error('Error toggling favorite, rolling back', error);
          set(state => ({
            conversations: state.conversations.map(conv =>
              conv.id === id ? { ...conv, isFavorite: !newFavoriteStatus } : conv
            ),
          }));
          throw error; // Re-throw for error handling in UI
        }
      },

      // Set active conversation
      setActiveConversation: id => {
        set({ activeConversationId: id });
        if (id) {
          get().loadMessages(id);
        }
      },

      // Load messages
      loadMessages: async conversationId => {
        try {
          set({ isLoading: true, error: null });

          const { data, error } = await supabase
            .from('chat_messages')
            .select('*')
            .eq('conversation_id', conversationId)
            .order('created_at', { ascending: true });

          if (error) throw error;

          const messages = data.map((msg: Record<string, unknown>) => ({
            id: msg.id,
            conversationId: msg.conversation_id,
            role: msg.role,
            content: msg.content,
            promptTokens: msg.prompt_tokens,
            completionTokens: msg.completion_tokens,
            totalTokens: msg.total_tokens,
            costUsd: parseFloat(msg.cost_usd || '0'),
            metadata: msg.metadata,
            createdAt: new Date(msg.created_at),
            synced: true,
            // Include attachments from metadata if available
            attachments:
              msg.has_attachments && msg.metadata?.attachments
                ? msg.metadata.attachments
                : undefined,
          }));

          set(state => ({
            messages: { ...state.messages, [conversationId]: messages },
            isLoading: false,
          }));

          logger.info('Messages loaded', 'ChatStore', {
            conversationId,
            count: messages.length,
          });
        } catch (error) {
          logger.error('Error loading messages', error);
          set({ error: error as Error, isLoading: false });
        }
      },

      // Cleanup streaming - CRITICAL FIX for memory leaks
      cleanupStreaming: () => {
        const state = get();
        if (state.streamingIntervalId) {
          clearInterval(state.streamingIntervalId);
        }
        set({
          streamingIntervalId: null,
          streamingMessage: null,
          typingStatus: 'idle',
        });
      },

      // Memory cleanup for conversation switching
      cleanupConversationMemory: (excludeConversationId?: string) => {
        set(state => {
          const cleanedMessages = excludeConversationId
            ? { [excludeConversationId]: state.messages[excludeConversationId] || [] }
            : {};

          return {
            messages: cleanedMessages,
            uploadingImages: {},
            error: null,
          };
        });
      },

      // Periodic memory cleanup
      scheduleMemoryCleanup: () => {
        const cleanup = () => {
          const state = get();
          const now = Date.now();
          const maxAge = 30 * 60 * 1000; // 30 minutes

          // Clean old messages except for active conversation
          const cleanedMessages: Record<string, ChatMessage[]> = {};

          Object.entries(state.messages).forEach(([conversationId, messages]) => {
            if (conversationId === state.activeConversationId) {
              // Keep all messages for active conversation
              cleanedMessages[conversationId] = messages;
            } else {
              // Keep only recent messages for inactive conversations
              cleanedMessages[conversationId] = messages.filter(
                msg => now - new Date(msg.createdAt).getTime() < maxAge
              );
            }
          });

          set({ messages: cleanedMessages });
        };

        // Schedule cleanup every 10 minutes
        return setInterval(cleanup, 10 * 60 * 1000);
      },

      // Send message
      sendMessage: async (content, conversationId, attachments) => {
        const state = get();
        let targetConversationId = conversationId || state.activeConversationId;

        // CRITICAL FIX: Always cleanup previous streaming to prevent memory leaks
        state.cleanupStreaming();

        // Create conversation if needed
        if (!targetConversationId) {
          // Generate a smart title based on the first message
          const smartTitle = state.generateSmartTitle(content);
          const newConv = await state.createConversation({ title: smartTitle });
          if (!newConv) return;
          targetConversationId = newConv.id;
        }

        // Add optimistic user message
        const userMessageId = state.addOptimisticMessage({
          conversationId: targetConversationId,
          role: 'user',
          content,
          hasAttachments: !!attachments && attachments.length > 0,
          attachments: attachments || [],
        });

        set({ isSending: true, error: null });

        try {
          // Call Edge Function
          const { user } = useAuthStore.getState();

          if (!user?.id || !user?.salonId) {
            throw new Error('Usuario o salón no configurado');
          }

          const payload = {
            conversationId: targetConversationId,
            message: content,
            salonId: user.salonId,
            userId: user.id,
            attachments:
              attachments?.map(att => ({
                type: att.type,
                url: att.url,
                mimeType: att.mimeType,
              })) || [],
          };

          logger.debug('Sending message with attachments', 'ChatStore', {
            hasAttachments: !!attachments && attachments.length > 0,
            attachmentCount: attachments?.length || 0,
            attachmentTypes: attachments?.map(a => a.type) || [],
            messageLength: content.length,
            conversationId: targetConversationId,
          });
          logger.info('Sending message to chat assistant', payload);

          const { data, error } = await supabase.functions.invoke('chat-assistant', {
            body: payload,
          });

          if (error) throw error;

          // Handle response from new Edge Function
          if (data.success === true && data.content) {
            logger.info('Received response from chat assistant');

            // Update user message as synced
            state.updateOptimisticMessage(userMessageId, {
              synced: true,
              promptTokens: data.usage?.promptTokens,
              totalTokens: data.usage?.totalTokens,
              costUsd: data.usage?.cost,
            });

            // Check if image was analyzed
            const hasImageAnalysis =
              attachments &&
              attachments.length > 0 &&
              (data.content.toLowerCase().includes('imagen') ||
                data.content.toLowerCase().includes('cabello') ||
                data.content.toLowerCase().includes('foto') ||
                data.content.toLowerCase().includes('observo') ||
                data.content.toLowerCase().includes('veo'));

            if (attachments && attachments.length > 0 && !hasImageAnalysis) {
              logger.warn('Image sent but no analysis detected in response');
            }

            // Create assistant message ID
            const assistantMessageId = `assistant_${Date.now()}`;

            // Start streaming simulation
            get().startStreaming(targetConversationId, assistantMessageId);

            // Set status to thinking initially
            get().setTypingStatus('thinking');

            // Small delay before starting to stream content
            setTimeout(() => {
              get().setTypingStatus('writing');

              // Simulate streaming by revealing content progressively
              const fullContent = data.content;
              let currentIndex = 0;
              const charsPerTick = 3; // Reveal 3 characters at a time
              const streamDelay = 20; // 20ms between updates

              const streamInterval = setInterval(() => {
                currentIndex = Math.min(currentIndex + charsPerTick, fullContent.length);

                get().updateStreamingContent(fullContent.slice(0, currentIndex));

                if (currentIndex >= fullContent.length) {
                  clearInterval(streamInterval);

                  // CRITICAL FIX: Use cleanup function to ensure proper cleanup
                  get().cleanupStreaming();

                  // Complete streaming and add final message
                  const assistantMessage: ChatMessage = {
                    id: assistantMessageId,
                    conversationId: targetConversationId,
                    role: 'assistant',
                    content: fullContent,
                    promptTokens: data.usage?.promptTokens,
                    completionTokens: data.usage?.completionTokens,
                    totalTokens: data.usage?.totalTokens,
                    costUsd: data.usage?.cost,
                    createdAt: new Date(),
                    synced: true,
                    metadata: data.state || {},
                  };

                  set(state => ({
                    messages: {
                      ...state.messages,
                      [targetConversationId]: [
                        ...(state.messages[targetConversationId] || []),
                        assistantMessage,
                      ],
                    },
                    conversations: state.conversations.map(conv =>
                      conv.id === targetConversationId
                        ? {
                            ...conv,
                            lastMessage:
                              assistantMessage.content.substring(0, 50) +
                              (assistantMessage.content.length > 50 ? '...' : ''),
                            lastMessageAt: new Date(),
                            metadata: {
                              ...conv.metadata,
                              flowState: data.state,
                            },
                          }
                        : conv
                    ),
                    streamingMessage: null,
                    typingStatus: 'idle',
                    isSending: false,
                  }));

                  // Generate intelligent title after first successful exchange
                  const conversation = get().conversations.find(c => c.id === targetConversationId);
                  const messageCount = (get().messages[targetConversationId] || []).length;

                  if (
                    conversation &&
                    messageCount <= 2 && // First exchange (user + assistant)
                    (conversation.title === 'Nueva conversación' ||
                      conversation.title.includes('Nueva conversación'))
                  ) {
                    // Generate intelligent title asynchronously (don't block UI)
                    setTimeout(() => {
                      get().generateIntelligentTitle(targetConversationId, content, fullContent);
                    }, 100);
                  }
                }
              }, streamDelay);

              // Store the interval ID so we can cancel it if needed
              set({ streamingIntervalId: streamInterval });

              // CRITICAL FIX: Add cleanup on unmount or conversation change
              // This will be called from components when needed
            }, 500); // 500ms delay before starting to write
          } else if (data.content) {
            // Fallback for old format
            logger.info('Received simple format response from chat assistant');

            // Update user message as synced
            state.updateOptimisticMessage(userMessageId, {
              synced: true,
              promptTokens: data.usage?.promptTokens,
              totalTokens: data.usage?.totalTokens,
              costUsd: data.usage?.cost,
            });

            // Add assistant response
            const assistantMessage: ChatMessage = {
              id: `assistant_${Date.now()}`,
              conversationId: targetConversationId,
              role: 'assistant',
              content: data.content,
              promptTokens: data.usage?.promptTokens,
              completionTokens: data.usage?.completionTokens,
              totalTokens: data.usage?.totalTokens,
              costUsd: data.usage?.cost,
              createdAt: new Date(),
              synced: true,
            };

            set(state => ({
              messages: {
                ...state.messages,
                [targetConversationId]: [
                  ...(state.messages[targetConversationId] || []),
                  assistantMessage,
                ],
              },
              conversations: state.conversations.map(conv =>
                conv.id === targetConversationId
                  ? {
                      ...conv,
                      lastMessage:
                        assistantMessage.content.substring(0, 50) +
                        (assistantMessage.content.length > 50 ? '...' : ''),
                      lastMessageAt: new Date(),
                    }
                  : conv
              ),
            }));

            // Generate intelligent title after first successful exchange (simple format)
            const conversation = get().conversations.find(c => c.id === targetConversationId);
            const messageCount = (get().messages[targetConversationId] || []).length;

            if (
              conversation &&
              messageCount <= 2 &&
              (conversation.title === 'Nueva conversación' ||
                conversation.title.includes('Nueva conversación'))
            ) {
              setTimeout(() => {
                get().generateIntelligentTitle(targetConversationId, content, data.content);
              }, 100);
            }
          } else if (data.assistantMessage) {
            // Old complex format
            logger.info('Received complex format response from chat assistant');

            // Update user message with server data
            if (data.userMessage) {
              state.updateOptimisticMessage(userMessageId, {
                id: data.userMessage.id,
                synced: true,
                promptTokens: data.userMessage.prompt_tokens,
                totalTokens: data.userMessage.total_tokens,
                costUsd: data.userMessage.cost_usd,
              });
            }

            const assistantMessage: ChatMessage = {
              id: data.assistantMessage.id,
              conversationId: targetConversationId,
              role: 'assistant',
              content: data.assistantMessage.content,
              promptTokens: data.assistantMessage.prompt_tokens,
              completionTokens: data.assistantMessage.completion_tokens,
              totalTokens: data.assistantMessage.total_tokens,
              costUsd: data.assistantMessage.cost_usd,
              metadata: data.assistantMessage.metadata,
              createdAt: new Date(data.assistantMessage.created_at),
              synced: true,
            };

            set(state => ({
              messages: {
                ...state.messages,
                [targetConversationId]: [
                  ...(state.messages[targetConversationId] || []),
                  assistantMessage,
                ],
              },
              conversations: state.conversations.map(conv =>
                conv.id === targetConversationId
                  ? {
                      ...conv,
                      lastMessage:
                        assistantMessage.content.substring(0, 50) +
                        (assistantMessage.content.length > 50 ? '...' : ''),
                      lastMessageAt: new Date(),
                    }
                  : conv
              ),
            }));

            // Update conversation stats and title if it's the first real exchange
            if (data.conversationStats) {
              set(state => {
                return {
                  conversations: state.conversations.map(conv =>
                    conv.id === targetConversationId
                      ? {
                          ...conv,
                          messageCount: data.conversationStats.message_count,
                          totalTokensUsed: data.conversationStats.total_tokens_used,
                          totalCostUsd: data.conversationStats.total_cost_usd,
                          lastMessageAt: new Date(),
                          updatedAt: new Date(),
                          lastMessage:
                            content.substring(0, 50) + (content.length > 50 ? '...' : ''),
                        }
                      : conv
                  ),
                };
              });

              // Generate intelligent title for first exchange (complex format)
              const conversation = get().conversations.find(c => c.id === targetConversationId);
              if (
                conversation &&
                conversation.title.startsWith('Nueva conversación') &&
                data.conversationStats.message_count <= 2
              ) {
                setTimeout(() => {
                  get().generateIntelligentTitle(
                    targetConversationId,
                    content,
                    data.assistantMessage.content
                  );
                }, 100);
              }
            }
          }

          set({ isSending: false });
        } catch (error) {
          logger.error('Error sending message', error);

          // Mark message as pending sync
          set(state => ({
            pendingSyncMessages: [
              ...state.pendingSyncMessages,
              state.messages[targetConversationId]?.find(m => m.localId === userMessageId)!,
            ],
            error: error as Error,
            isSending: false,
          }));
        }
      },

      // Add optimistic message
      addOptimisticMessage: message => {
        const localId = `local_${Date.now()}_${Math.random()}`;
        const optimisticMessage: ChatMessage = {
          ...message,
          id: localId,
          localId,
          createdAt: new Date(),
          synced: false,
        };

        set(state => ({
          messages: {
            ...state.messages,
            [message.conversationId]: [
              ...(state.messages[message.conversationId] || []),
              optimisticMessage,
            ],
          },
        }));

        return localId;
      },

      // Update optimistic message
      updateOptimisticMessage: (localId, updates) => {
        set(state => {
          const newMessages = { ...state.messages };

          for (const conversationId in newMessages) {
            newMessages[conversationId] = newMessages[conversationId].map(msg =>
              msg.localId === localId ? { ...msg, ...updates } : msg
            );
          }

          return { messages: newMessages };
        });
      },

      // Add context reference
      addContextReference: async (messageId, reference) => {
        try {
          const { error } = await supabase.from('chat_context_references').insert({
            message_id: messageId,
            ...reference,
          });

          if (error) throw error;

          logger.info('Context reference added', 'ChatStore', {
            messageId,
            type: reference.referenceType,
          });
        } catch (error) {
          logger.error('Error adding context reference', error);
        }
      },

      // Sync pending messages
      syncPendingMessages: async () => {
        const state = get();
        if (state.pendingSyncMessages.length === 0) return;

        logger.info('Syncing pending messages', 'ChatStore', {
          count: state.pendingSyncMessages.length,
        });

        for (const message of state.pendingSyncMessages) {
          try {
            // eslint-disable-next-line no-await-in-loop -- Sequential message sync required to maintain conversation order
            await state.sendMessage(message.content, message.conversationId);

            set(state => ({
              pendingSyncMessages: state.pendingSyncMessages.filter(
                m => m.localId !== message.localId
              ),
            }));
          } catch (error) {
            logger.error('Error syncing message', {
              messageId: message.localId,
              error,
            });
          }
        }

        set({ lastSync: new Date() });
      },

      // Upload chat image
      uploadChatImage: async (imageUri, messageId) => {
        try {
          logger.debug('Starting chat image upload', 'ChatStore', {
            imageUri: imageUri.substring(0, 50) + '...',
            messageId,
          });

          const { user } = useAuthStore.getState();
          if (!user?.id || !user?.salonId) {
            throw new Error('Usuario o salón no configurado');
          }

          // Generate unique filename
          const timestamp = Date.now();
          const randomId = Math.random().toString(36).substring(7);
          const fileName = `chat/${user.salonId}/${timestamp}-${randomId}.jpg`;
          logger.debug('Generated filename', 'ChatStore', { fileName });

          // Get the image data
          let uploadData: ArrayBuffer;

          if (imageUri.startsWith('data:image')) {
            logger.debug('Processing base64 data URI');
            // Handle base64 data URI - extract just the base64 part
            const base64 = imageUri.split(',')[1];

            if (!base64) {
              throw new Error('Invalid base64 data URI - missing data after comma');
            }

            logger.debug('Base64 length', 'ChatStore', {
              length: base64.length,
            });

            // Decode base64 for React Native
            uploadData = decode(base64);

            logger.debug('Decoded ArrayBuffer', 'ChatStore', {
              byteLength: uploadData.byteLength,
            });
          } else {
            // Handle regular URI - should not happen in our case
            // since ImageProcessor returns base64
            throw new Error(`Expected base64 data URI, got: ${imageUri.substring(0, 100)}`);
          }

          // Upload to Supabase Storage (Private bucket)
          logger.debug('Starting Supabase Storage upload to private bucket', 'ChatStore', {
            fileName,
            byteLength: uploadData.byteLength,
          });

          const { data, error } = await supabase.storage
            .from('service-photos-private')
            .upload(fileName, uploadData, {
              contentType: 'image/jpeg',
              cacheControl: '3600',
            });

          if (error) {
            logger.error('Supabase Storage upload failed', error);
            throw error;
          }

          logger.debug('Supabase Storage upload successful', data);

          // Generate signed URL for private bucket (1 hour expiration)
          const { data: signedData, error: signedError } = await supabase.storage
            .from('service-photos-private')
            .createSignedUrl(fileName, 3600); // 1 hour

          if (signedError) {
            logger.error('Failed to generate signed URL', signedError);
            throw signedError;
          }

          if (!signedData?.signedUrl) {
            throw new Error('No signed URL returned from Supabase');
          }

          logger.debug('Generated signed URL', 'ChatStore', {
            signedUrl: signedData.signedUrl.substring(0, 100) + '...',
          });

          const attachment: ChatAttachment = {
            type: 'image',
            url: signedData.signedUrl,
            fileName: fileName.split('/').pop(),
            fileSize: uploadData.byteLength || 0,
            mimeType: 'image/jpeg',
            uploadStatus: 'completed',
          };

          logger.info('Chat image uploaded successfully', 'ChatStore', {
            fileName,
            messageId,
            fileSize: attachment.fileSize,
          });
          return attachment;
        } catch (error) {
          logger.error('Error uploading chat image', error);
          set({ error: error as Error });
          return null;
        }
      },

      // Update attachment status
      updateAttachmentStatus: (messageId, attachmentIndex, status) => {
        set(state => {
          const newMessages = { ...state.messages };

          for (const conversationId in newMessages) {
            newMessages[conversationId] = newMessages[conversationId].map(msg => {
              if (msg.id === messageId || msg.localId === messageId) {
                const attachments = [...(msg.attachments || [])];
                if (attachments[attachmentIndex]) {
                  attachments[attachmentIndex] = {
                    ...attachments[attachmentIndex],
                    uploadStatus: status,
                  };
                }
                return { ...msg, attachments };
              }
              return msg;
            });
          }

          return { messages: newMessages };
        });
      },

      // Generate smart title from message content (fallback method)
      generateSmartTitle: (content: string) => {
        // Extract key terms for title
        const lowerContent = content.toLowerCase();

        // Check for specific keywords
        if (lowerContent.includes('stock') || lowerContent.includes('inventario')) {
          return 'Consulta de inventario';
        }
        if (lowerContent.includes('fórmula') || lowerContent.includes('formula')) {
          return 'Consulta sobre fórmula';
        }
        if (lowerContent.includes('cliente')) {
          return 'Consulta sobre cliente';
        }
        if (lowerContent.includes('rubio') || lowerContent.includes('rubia')) {
          return 'Fórmula rubio';
        }
        if (lowerContent.includes('cana') || lowerContent.includes('gris')) {
          return 'Cobertura de canas';
        }
        if (lowerContent.includes('rojo') || lowerContent.includes('cobrizo')) {
          return 'Tonos rojizos';
        }
        if (lowerContent.includes('mechas') || lowerContent.includes('balayage')) {
          return 'Técnica de mechas';
        }
        if (lowerContent.includes('corrección') || lowerContent.includes('corregir')) {
          return 'Corrección de color';
        }

        // Default: Use first 40 characters
        const cleanContent = content.replace(/\s+/g, ' ').trim();
        if (cleanContent.length > 40) {
          return cleanContent.substring(0, 40) + '...';
        }
        return cleanContent || 'Nueva conversación';
      },

      // Generate intelligent title using AI (Claude-style)
      generateIntelligentTitle: async (
        conversationId: string,
        userMessage: string,
        assistantResponse: string
      ) => {
        try {
          const { user } = useAuthStore.getState();
          if (!user?.salonId) return;

          // Check if we should generate title (only for new conversations)
          const conversation = get().conversations.find(c => c.id === conversationId);
          if (
            !conversation ||
            (!conversation.title.includes('Nueva conversación') &&
              conversation.title !== 'Nueva conversación')
          ) {
            return; // Skip if already has custom title
          }

          // Call the title generation Edge Function
          const { data, error } = await supabase.functions.invoke('generate-title', {
            body: {
              conversationId,
              userMessage,
              assistantResponse,
              salonId: user.salonId,
            },
          });

          if (error) {
            logger.warn('Error generating intelligent title, using fallback', error);
            // Use fallback method
            const fallbackTitle = get().generateSmartTitle(userMessage);
            await get().updateConversation(conversationId, { title: fallbackTitle });
            return;
          }

          if (data?.success && data?.title) {
            // Update local state immediately
            set(state => ({
              conversations: state.conversations.map(conv =>
                conv.id === conversationId ? { ...conv, title: data.title } : conv
              ),
            }));

            logger.info('Intelligent title generated', 'ChatStore', {
              conversationId,
              title: data.title,
              cost: data.usage?.cost || 0,
            });
          }
        } catch (error) {
          logger.warn('Failed to generate intelligent title, using fallback', error);
          // Use fallback method
          const fallbackTitle = get().generateSmartTitle(userMessage);
          await get().updateConversation(conversationId, { title: fallbackTitle });
        }
      },

      // Streaming actions implementation
      startStreaming: (conversationId, messageId) => {
        set({
          streamingMessage: {
            conversationId,
            messageId,
            content: '',
            isComplete: false,
            currentIndex: 0,
          },
          typingStatus: 'thinking',
        });
      },

      updateStreamingContent: (content, append = false) => {
        set(state => {
          if (!state.streamingMessage) return state;
          return {
            streamingMessage: {
              ...state.streamingMessage,
              content: append ? state.streamingMessage.content + content : content,
              currentIndex: content.length,
            },
            typingStatus: 'writing',
          };
        });
      },

      completeStreaming: () => {
        set(state => {
          if (!state.streamingMessage) return { typingStatus: 'idle' };

          const { conversationId, messageId, content } = state.streamingMessage;

          // Add the complete message to the conversation
          const assistantMessage: ChatMessage = {
            id: messageId,
            conversationId,
            role: 'assistant',
            content,
            createdAt: new Date(),
            synced: true,
          };

          return {
            messages: {
              ...state.messages,
              [conversationId]: [...(state.messages[conversationId] || []), assistantMessage],
            },
            streamingMessage: null,
            typingStatus: 'idle',
          };
        });
      },

      setTypingStatus: status => {
        set({ typingStatus: status });
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Reset store
      reset: () => set(initialState),
    }),
    {
      name: 'chat-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        activeConversationId: state.activeConversationId,
        pendingSyncMessages: state.pendingSyncMessages,
        lastSync: state.lastSync,
      }),
    }
  )
);
