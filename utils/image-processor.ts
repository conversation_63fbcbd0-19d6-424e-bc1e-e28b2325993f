/**
 * Image Processor Service
 *
 * Servicio centralizado para el procesamiento de imágenes
 * Maneja compresión, validación de calidad y generación de hashes
 */

import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import * as Crypto from 'expo-crypto';
import { logger } from './logger';
import { applyPrivacyFilter as applyPrivacyFilterUtil, getPrivacyLevel } from './privacy-filter';

// Configuración de compresión por propósito
const COMPRESSION_PROFILES = {
  diagnosis: {
    maxWidth: 400,
    quality: 0.5,
    maxSizeKB: 500,
    fallbackWidth: 300,
    fallbackQuality: 0.3,
  },
  desired: {
    maxWidth: 350,
    quality: 0.4,
    maxSizeKB: 400,
    fallbackWidth: 250,
    fallbackQuality: 0.3,
  },
  thumbnail: {
    maxWidth: 150,
    quality: 0.6,
    maxSizeKB: 50,
    fallbackWidth: 100,
    fallbackQuality: 0.4,
  },
  storage: {
    maxWidth: 800,
    quality: 0.7,
    maxSizeKB: 1000,
    fallbackWidth: 600,
    fallbackQuality: 0.5,
  },
  upload: {
    maxWidth: 1280,
    quality: 0.7,
    maxSizeKB: 2000,
    fallbackWidth: 1024,
    fallbackQuality: 0.6,
  },
  chat: {
    maxWidth: 1024,
    quality: 0.65, // Slightly higher quality for premium chat experience
    maxSizeKB: 250, // Increased size limit for better quality
    fallbackWidth: 800,
    fallbackQuality: 0.45, // Better fallback quality
  },
  // New premium thumbnail profile for chat previews
  chatThumbnail: {
    maxWidth: 200,
    quality: 0.8, // High quality for small thumbnails
    maxSizeKB: 60,
    fallbackWidth: 150,
    fallbackQuality: 0.65,
  },
} as const;

export type CompressionPurpose = keyof typeof COMPRESSION_PROFILES;

interface ValidationIssue {
  type: 'size' | 'dimension' | 'format' | 'quality';
  message: string;
  severity: 'warning' | 'error';
}

interface CompressionResult {
  base64: string;
  sizeKB: number;
  width: number;
  height: number;
  compressionRatio: number;
}

export class ImageProcessor {
  private static compressionCache = new Map<string, CompressionResult>();
  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutos
  private static readonly LOCAL_URI_TTL = 10 * 60 * 1000; // 10 minutos
  private static localUriCache = new Map<string, { localUri: string; expiresAt: number }>();

  private static getCachedLocalUri = async (key: string): Promise<string | null> => {
    const hit = this.localUriCache.get(key);
    if (!hit) return null;
    const now = Date.now();
    if (hit.expiresAt <= now) {
      // Expirado: intentar borrar archivo y limpiar entrada
      try {
        await FileSystem.deleteAsync(hit.localUri, { idempotent: true });
      } catch {}
      this.localUriCache.delete(key);
      return null;
    }
    // Validar que el archivo aún exista
    try {
      const info = await FileSystem.getInfoAsync(hit.localUri);
      if (!info.exists) {
        this.localUriCache.delete(key);
        return null;
      }
    } catch {
      this.localUriCache.delete(key);
      return null;
    }
    return hit.localUri;
  };

  private static putCachedLocalUri = (key: string, localUri: string, ttlMs?: number) => {
    const expiresAt = Date.now() + (ttlMs ?? this.LOCAL_URI_TTL);
    this.localUriCache.set(key, { localUri, expiresAt });
  };

  /**
   * Asegura que la URI de entrada sea un archivo local (`file://`) que
   * expo-image-manipulator pueda procesar de forma confiable.
   * - http/https: descarga a caché
   * - content:// (Android) u otras: copia a caché
   * - data:...;base64,...: escribe a archivo temporal
   * - file://: se usa tal cual
   */
  private static async ensureLocalFile(
    uri: string
  ): Promise<{ localUri: string; cleanup?: () => Promise<void> }> {
    try {
      // Manejo especial para iOS Photos: ph://
      if (uri.startsWith('ph://')) {
        // Intentar usar ImageManipulator para exportar una copia a archivo local
        try {
          const res = await ImageManipulator.manipulateAsync(uri, [{ rotate: 0 }], {
            compress: 1,
            format: ImageManipulator.SaveFormat.JPEG,
          });
          if (res?.uri?.startsWith('file://')) {
            return { localUri: res.uri };
          }
        } catch {}
        // Si no podemos, continuamos con el flujo general y copiaremos a caché si es posible
      }
      // file:// ya es local
      if (uri.startsWith('file://')) {
        return { localUri: uri };
      }

      // Cache: ¿ya tenemos una conversión reciente?
      const cached = await this.getCachedLocalUri(uri);
      if (cached) {
        return { localUri: cached };
      }

      // data URL -> escribir a archivo
      if (uri.startsWith('data:')) {
        const match = uri.match(/^data:(.*?);base64,(.*)$/);
        const base64 = match?.[2];
        const extFromMime = (match?.[1] || 'image/jpeg').split('/')[1] || 'jpg';
        const tempPath = `${FileSystem.cacheDirectory}img-${Date.now()}-${Math.random()
          .toString(36)
          .slice(2)}.${extFromMime}`;
        if (!base64) throw new Error('Invalid data URI');
        const base64Enc =
          (FileSystem as unknown as { EncodingType?: { Base64?: string } }).EncodingType?.Base64 ??
          'base64';
        await FileSystem.writeAsStringAsync(tempPath, base64, { encoding: base64Enc });
        this.putCachedLocalUri(uri, tempPath);
        return { localUri: tempPath };
      }

      // http/https -> descargar a caché
      if (uri.startsWith('http://') || uri.startsWith('https://')) {
        const extMatch = uri.split('?')[0].split('#')[0].split('.').pop();
        const ext = extMatch && extMatch.length < 6 ? extMatch : 'jpg';
        const target = `${FileSystem.cacheDirectory}dl-${Date.now()}-${Math.random()
          .toString(36)
          .slice(2)}.${ext}`;

        try {
          const { uri: localUri } = await FileSystem.downloadAsync(uri, target);

          // Verify the downloaded file exists and has content
          const info = await FileSystem.getInfoAsync(localUri);
          if (!info.exists || (info.size && info.size < 100)) {
            throw new Error(`Downloaded file is invalid or too small (${info.size} bytes)`);
          }

          this.putCachedLocalUri(uri, localUri);
          return { localUri };
        } catch (downloadError) {
          logger.error('Failed to download image from URL:', 'ImageProcessor', downloadError);
          throw new Error(`No se pudo descargar la imagen: ${downloadError instanceof Error ? downloadError.message : String(downloadError)}`);
        }
      }

      // content:// u otros esquemas -> copiar a caché (Android)
      const ext = 'jpg';
      const target = `${FileSystem.cacheDirectory}cp-${Date.now()}-${Math.random()
        .toString(36)
        .slice(2)}.${ext}`;
      await FileSystem.copyAsync({ from: uri, to: target });
      this.putCachedLocalUri(uri, target);
      return { localUri: target };
    } catch {
      // Si algo falla, retornamos la URI original para evitar bloquear el flujo
      return { localUri: uri };
    }
  }

  /**
   * Comprime una imagen según el propósito especificado
   * Utiliza cache para evitar recompresiones innecesarias
   */
  static async compressForAI(uri: string, purpose: 'diagnosis' | 'desired'): Promise<string> {
    const cacheKey = `${uri}-${purpose}`;
    const cached = this.compressionCache.get(cacheKey);

    if (cached && this.isCacheValid(cacheKey)) {
      logger.debug('Using cached compression for:', 'ImageProcessor', {
        purpose,
      });
      return cached.base64;
    }

    const profile = COMPRESSION_PROFILES[purpose];

    try {
      logger.debug(`Compressing image for ${purpose}...`, 'ImageProcessor');

      // Primera compresión
      let result = await this.compress(uri, profile.maxWidth, profile.quality);

      // Si excede el tamaño máximo, recomprimir
      if (result.sizeKB > profile.maxSizeKB) {
        logger.warn(`Image too large (${result.sizeKB}KB), recompressing...`, 'ImageProcessor');
        result = await this.compress(uri, profile.fallbackWidth, profile.fallbackQuality);

        // Si aún es muy grande, compresión agresiva
        if (result.sizeKB > profile.maxSizeKB) {
          logger.warn('Applying aggressive compression...', 'ImageProcessor');
          result = await this.compress(uri, profile.fallbackWidth * 0.8, 0.25);
        }
      }

      logger.info(
        `Final compression: ${result.sizeKB}KB (${result.compressionRatio.toFixed(1)}x reduction)`,
        'ImageProcessor'
      );

      // Guardar en cache
      this.compressionCache.set(cacheKey, result);
      setTimeout(() => this.compressionCache.delete(cacheKey), this.CACHE_TTL);

      return result.base64;
    } catch (error) {
      logger.error('Compression failed:', 'ImageProcessor', error);

      // Provide more specific error information
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('File') && errorMessage.includes('is not readable')) {
        throw new Error('La imagen no se puede leer. Por favor, toma una nueva foto o selecciona otra imagen.');
      } else if (errorMessage.includes('downloadAsync') || errorMessage.includes('getInfoAsync')) {
        throw new Error('Error de compatibilidad con el sistema de archivos. Por favor, reinicia la aplicación e intenta de nuevo.');
      } else {
        throw new Error(`Error al comprimir la imagen: ${errorMessage}`);
      }
    }
  }

  /**
   * Valida la calidad y características de una imagen
   */
  static async validateQuality(uri: string): Promise<{
    valid: boolean;
    issues: ValidationIssue[];
  }> {
    const issues: ValidationIssue[] = [];

    try {
      // Asegurar archivo local y obtener información sin transformaciones
      const { localUri, cleanup } = await this.ensureLocalFile(uri);
      const info = await ImageManipulator.manipulateAsync(localUri, [], {
        base64: false,
      });
      if (cleanup) await cleanup();

      // Validar dimensiones
      if (info.width < 200 || info.height < 200) {
        issues.push({
          type: 'dimension',
          message: 'La imagen es demasiado pequeña (mínimo 200x200)',
          severity: 'error',
        });
      } else if (info.width < 400 || info.height < 400) {
        issues.push({
          type: 'dimension',
          message: 'Se recomienda una imagen de al menos 400x400 para mejor análisis',
          severity: 'warning',
        });
      }

      // Validar proporción
      const aspectRatio = info.width / info.height;
      if (aspectRatio < 0.5 || aspectRatio > 2) {
        issues.push({
          type: 'dimension',
          message: 'La proporción de la imagen no es óptima',
          severity: 'warning',
        });
      }

      // Estimar tamaño del archivo con fallback
      let sizeKB = 0;
      try {
        const { localUri: sizeUri, cleanup: sizeCleanup } = await this.ensureLocalFile(uri);
        const tempResult = await ImageManipulator.manipulateAsync(sizeUri, [], {
          base64: true,
          compress: 1,
        });
        if (sizeCleanup) await sizeCleanup();

        if (tempResult.base64) {
          sizeKB = (tempResult.base64.length * 3) / 4 / 1024;
        }
      } catch (sizeError) {
        logger.warn(
          'Could not estimate image size, skipping size validation:',
          'ImageProcessor',
          sizeError
        );
        issues.push({
          type: 'processing',
          message: 'No se pudo estimar el tamaño de la imagen, pero se procesará igualmente',
          severity: 'warning',
        });
      }

      if (sizeKB > 5000) {
        issues.push({
          type: 'size',
          message: 'La imagen es demasiado grande (máximo 5MB)',
          severity: 'error',
        });
      }

      // Validación básica de calidad por tamaño
      // (imágenes muy pequeñas en KB suelen ser de baja calidad)
      if (sizeKB < 50 && info.width > 500) {
        issues.push({
          type: 'quality',
          message: 'La imagen parece tener muy baja calidad',
          severity: 'warning',
        });
      }

      const hasErrors = issues.some(i => i.severity === 'error');

      return {
        valid: !hasErrors,
        issues,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Detectar error específico de Babel construct
      if (errorMessage.includes('construct') || errorMessage.includes('Reflect.construct')) {
        logger.warn(
          'ImageManipulator validation failed due to Babel construct issue, allowing image:',
          'ImageProcessor',
          error
        );
        return {
          valid: true, // Allow the image to proceed
          issues: [
            {
              type: 'processing',
              message:
                'Validación de imagen limitada debido a problemas de compatibilidad - imagen aceptada',
              severity: 'warning',
            },
          ],
        };
      }

      logger.error('Quality validation failed:', 'ImageProcessor', error);
      return {
        valid: false,
        issues: [
          {
            type: 'format',
            message: 'No se pudo validar la imagen',
            severity: 'error',
          },
        ],
      };
    }
  }

  /**
   * Genera un hash único para una imagen base64
   * Útil para cache y deduplicación
   */
  static async generateHash(base64: string): Promise<string> {
    try {
      // Tomar solo una porción del base64 para el hash (más eficiente)
      const sample = base64.substring(0, 1000) + base64.length;
      const hash = await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, sample);
      return hash.substring(0, 16); // Primeros 16 caracteres del hash
    } catch (error) {
      logger.error('Hash generation failed:', 'ImageProcessor', error);
      // Fallback a un hash simple
      return `${base64.length}-${Date.now()}`;
    }
  }

  /**
   * Comprime una imagen con los parámetros especificados
   */
  private static async compress(
    uri: string,
    width: number,
    quality: number
  ): Promise<CompressionResult> {
    const startTime = Date.now();

    try {
      const { localUri, cleanup } = await this.ensureLocalFile(uri);
      const targetWidth = Math.max(1, Math.round(width));
      const result = await ImageManipulator.manipulateAsync(
        localUri,
        [{ resize: { width: targetWidth } }],
        {
          compress: quality,
          format: ImageManipulator.SaveFormat.JPEG,
          base64: true,
        }
      );
      if (!result.base64) {
        throw new Error('ImageManipulator failed to generate base64 data');
      }

      const processed = await this.processCompressionResult(
        result,
        startTime,
        localUri,
        width,
        quality
      );
      if (cleanup) await cleanup();
      return processed;
    } catch (error) {
      logger.warn('ImageManipulator compression failed, using fallback:', 'ImageProcessor', error);

      // Fallback: return original image with minimal processing
      return this.createFallbackCompressionResult(uri, startTime);
    }
  }

  /**
   * Procesa el resultado de compresión exitosa
   */
  private static async processCompressionResult(
    result: ImageManipulator.ImageResult,
    startTime: number,
    uri: string,
    _width: number,
    _quality: number
  ): Promise<CompressionResult> {
    const sizeKB = (result.base64.length * 3) / 4 / 1024;

    // Intentar obtener tamaño original del archivo directamente
    let originalSizeKB = sizeKB * 2; // Estimación por defecto
    try {
      const info = await FileSystem.getInfoAsync(uri);
      const fileInfo = info as FileSystem.FileInfo & { size?: number };
      if (fileInfo?.size && typeof fileInfo.size === 'number') {
        originalSizeKB = Math.round((fileInfo.size / 1024) * 10) / 10;
      } else {
        // Fallback: usar manipulateAsync sin cambios y compress=1
        const originalResult = await ImageManipulator.manipulateAsync(uri, [], {
          base64: true,
          compress: 1,
        });
        if (originalResult.base64) {
          originalSizeKB = Math.round(((originalResult.base64.length * 3) / 4 / 1024) * 10) / 10;
        }
      }
    } catch (error) {
      logger.warn('Could not get original size, using estimation:', 'ImageProcessor', error);
    }

    logger.debug(`Compression took ${Date.now() - startTime}ms`, 'ImageProcessor');

    return {
      base64: result.base64!,
      sizeKB: Math.round(sizeKB * 10) / 10,
      width: result.width,
      height: result.height,
      compressionRatio: originalSizeKB / sizeKB,
    };
  }

  /**
   * Crea un resultado de compresión fallback cuando ImageManipulator falla
   */
  private static async createFallbackCompressionResult(
    uri: string,
    _startTime: number
  ): Promise<CompressionResult> {
    try {
      // Asegurar archivo local accesible
      const { localUri, cleanup } = await this.ensureLocalFile(uri);
      try {
        // Si por algún motivo aún no tenemos un file://, intentar descargar si es http(s)
        let candidateUri = localUri;
        if (!candidateUri.startsWith('file://')) {
          if (uri.startsWith('http://') || uri.startsWith('https://')) {
            try {
              const ext = 'jpg';
              const target = `${FileSystem.cacheDirectory}dl-fb-${Date.now()}-${Math.random()
                .toString(36)
                .slice(2)}.${ext}`;
              const { uri: dlUri } = await FileSystem.downloadAsync(uri, target);
              candidateUri = dlUri;
            } catch {}
          }
        }

        // Comprobar existencia antes de leer
        const info = await FileSystem.getInfoAsync(candidateUri);
        if (!info.exists) throw new Error('Local fallback file does not exist');

        // Leer contenido como base64 directamente del archivo
        const base64Enc =
          (FileSystem as unknown as { EncodingType?: { Base64?: string } }).EncodingType?.Base64 ??
          'base64';
        const base64 = await FileSystem.readAsStringAsync(candidateUri, { encoding: base64Enc });
        const sizeKB = (base64.length * 3) / 4 / 1024;
        logger.info('Used fallback compression method', 'ImageProcessor');
        return {
          base64,
          sizeKB: Math.round(sizeKB * 10) / 10,
          width: 400, // Dimensiones estimadas si no podemos leer metadatos
          height: 400,
          compressionRatio: 1,
        };
      } finally {
        if (cleanup) await cleanup();
      }
    } catch (error) {
      logger.error('Fallback compression also failed:', 'ImageProcessor', error);
      // Instead of returning a 1x1 transparent image that causes AI analysis issues,
      // throw an error to prevent sending corrupted images to AI
      throw new Error(`Image compression failed completely. Original error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Verifica si una entrada de cache sigue siendo válida
   */
  private static isCacheValid(key: string): boolean {
    // Por ahora, confiamos en el setTimeout para limpiar el cache
    // Podríamos añadir timestamps si necesitamos más control
    return this.compressionCache.has(key);
  }

  /**
   * Limpia el cache de compresión
   */
  static clearCache(): void {
    this.compressionCache.clear();
    logger.info('Image compression cache cleared', 'ImageProcessor');
  }

  /**
   * Obtiene estadísticas del cache
   */
  static getCacheStats(): { size: number; entries: string[] } {
    return {
      size: this.compressionCache.size,
      entries: Array.from(this.compressionCache.keys()),
    };
  }

  /**
   * Limpia archivos temporales antiguos del directorio de caché (dl-*, cp-*, img-*, dl-fb-*).
   * Elimina aquellos con más de 24h.
   */
  static async cleanupOldTempFiles(maxAgeMs = 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const dir = FileSystem.cacheDirectory;
      if (!dir) return;
      const entries = await FileSystem.readDirectoryAsync(dir);
      const prefixes = ['dl-', 'cp-', 'img-', 'dl-fb-'];
      const now = Date.now();
      // OPTIMIZED: Process file operations in parallel with batch processing
      const batchSize = 20; // Limit concurrent file operations
      const filesToProcess = entries.filter(name => prefixes.some(p => name.startsWith(p)));

      for (let i = 0; i < filesToProcess.length; i += batchSize) {
        const batch = filesToProcess.slice(i, i + batchSize);

        const batchPromises = batch.map(async name => {
          const path = dir + name;
          try {
            const info = await FileSystem.getInfoAsync(path);
            let modifiedMs: number | null = null;
            const fileInfo = info as FileSystem.FileInfo & { modificationTime?: number };
            if ('modificationTime' in info && fileInfo.modificationTime) {
              modifiedMs = fileInfo.modificationTime * 1000;
            }
            if (!modifiedMs) {
              // Intentar parsear timestamp desde el nombre (ej: dl-<ms>-random.ext)
              const m = name.match(/^[a-z-]+-(\d+)-/);
              if (m && m[1]) {
                const ts = parseInt(m[1], 10);
                if (!Number.isNaN(ts)) modifiedMs = ts > 1e12 ? ts : ts * 1000;
              }
            }
            if (!modifiedMs) return;
            if (now - modifiedMs > maxAgeMs) {
              await FileSystem.deleteAsync(path, { idempotent: true });
            }
          } catch {
            // Silently ignore file deletion errors
          }
        });

        // Process batch in parallel
        // eslint-disable-next-line no-await-in-loop -- Batch processing with controlled concurrency
        await Promise.all(batchPromises);
      }
      // Purge expired in-memory cache
      for (const [k, v] of Array.from(this.localUriCache.entries())) {
        if (v.expiresAt <= now) this.localUriCache.delete(k);
      }
      logger.debug('Old temp files cleanup complete', 'ImageProcessor');
    } catch (error) {
      logger.warn('Temp files cleanup failed', 'ImageProcessor', error);
    }
  }

  /**
   * Comprime una imagen para subida con opción de aplicar filtro de privacidad
   * @param uri - URI de la imagen
   * @param applyPrivacyFilter - Si aplicar pixelado en la región superior
   * @param profile - Perfil de compresión a usar ('upload' por defecto, 'chat' para chat assistant)
   * @returns Base64 de la imagen procesada
   */
  static async compressForUpload(
    uri: string,
    applyPrivacyFilter = true,
    profile: 'upload' | 'chat' = 'upload'
  ): Promise<string> {
    try {
      logger.debug('Compressing image for upload...', 'ImageProcessor');

      // Primero aplicar filtro de privacidad si es necesario
      let processedUri = uri;
      if (applyPrivacyFilter) {
        processedUri = await this.applyPrivacyFilter(uri);
      }

      // Luego comprimir usando el perfil especificado
      const compressionProfile = COMPRESSION_PROFILES[profile];
      let result = await this.compress(
        processedUri,
        compressionProfile.maxWidth,
        compressionProfile.quality
      );

      // Si excede el tamaño, recomprimir
      if (result.sizeKB > compressionProfile.maxSizeKB) {
        logger.warn(`Image too large (${result.sizeKB}KB), recompressing...`, 'ImageProcessor');
        result = await this.compress(
          processedUri,
          compressionProfile.fallbackWidth,
          compressionProfile.fallbackQuality
        );
      }

      logger.info(`Upload compression complete: ${result.sizeKB}KB`, 'ImageProcessor');
      return result.base64;
    } catch (error) {
      logger.error('Upload compression failed:', 'ImageProcessor', error);
      throw new Error('Error al procesar la imagen para subida');
    }
  }

  /**
   * Creates a premium thumbnail optimized for chat interface
   * @param uri - URI of the original image
   * @returns Base64 of the optimized thumbnail
   */
  static async createChatThumbnail(uri: string): Promise<string> {
    try {
      logger.debug('Creating premium chat thumbnail...', 'ImageProcessor');

      const profile = COMPRESSION_PROFILES.chatThumbnail;
      let result = await this.compress(uri, profile.maxWidth, profile.quality);

      // Apply additional optimization for thumbnails
      if (result.sizeKB > profile.maxSizeKB) {
        result = await this.compress(uri, profile.fallbackWidth, profile.fallbackQuality);
      }

      logger.info(`Premium thumbnail created: ${result.sizeKB}KB`, 'ImageProcessor');
      return result.base64;
    } catch (error) {
      logger.error('Thumbnail creation failed:', 'ImageProcessor', error);
      throw new Error('Error al crear miniatura de imagen');
    }
  }

  /**
   * Aplica un filtro de privacidad pixelando la región superior de la imagen
   * @param uri - URI de la imagen original
   * @returns URI de la imagen con filtro aplicado
   */
  private static async applyPrivacyFilter(uri: string): Promise<string> {
    try {
      logger.debug('Applying privacy filter...', 'ImageProcessor');

      // Usar el nuevo sistema de filtro de privacidad con nivel bajo para IA
      const privacyOptions = getPrivacyLevel('ai_analysis');
      const { localUri, cleanup } = await this.ensureLocalFile(uri);
      const filteredUri = await applyPrivacyFilterUtil(localUri, privacyOptions);
      if (cleanup) await cleanup();

      logger.debug('Privacy filter applied successfully', 'ImageProcessor');
      return filteredUri;
    } catch (error) {
      logger.error('Privacy filter failed:', 'ImageProcessor', error);
      // Si falla, devolver imagen original
      return uri;
    }
  }
}
