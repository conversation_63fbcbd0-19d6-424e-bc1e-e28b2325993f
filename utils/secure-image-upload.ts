/**
 * Secure Image Upload Utility
 *
 * Handles the secure upload flow:
 * 1. Process and compress image locally
 * 2. Call simple upload Edge Function
 * 3. Return public URL of uploaded image
 *
 * Processing moved to client for 100% reliability
 */

import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import { useAuthStore } from '@/stores/auth-store';
import { ImageProcessor } from '@/utils/image-processor';
import { getSignedUrl } from '@/utils/signed-url-manager';

interface SecureUploadOptions {
  clientId: string;
  photoType: 'before' | 'after' | 'desired';
  onProgress?: (progress: number) => void;
}

interface SecureUploadResult {
  success: boolean;
  publicUrl?: string; // Legacy support
  signedUrl?: string; // New private bucket signed URL
  bucket?: string;
  path?: string;
  expiresAt?: number;
  error?: string;
}

export class SecureImageUpload {
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAY = 1000;
  private static readonly USE_PRIVATE_BUCKETS = true; // ENFORCED: Always use private buckets for GDPR compliance

  /**
   * Main entry point for secure image upload
   */
  static async uploadAndAnonymize(
    imageUri: string,
    options: SecureUploadOptions
  ): Promise<SecureUploadResult> {
    const { clientId, photoType, onProgress } = options;

    try {
      // Starting secure upload process
      // Client and photo type validated

      // Step 1: Get current salon ID
      // Getting salon ID
      const salonId = await this.getCurrentSalonId();
      if (!salonId) {
        throw new Error('No salon ID available. Please ensure you are logged in.');
      }
      // Salon ID obtained

      // Step 2: Process image locally (compress + optional privacy filter)
      onProgress?.(20);
      // Processing image locally
      const startTime = Date.now();

      // Always apply privacy filter to ensure consistency and privacy-first policy
      const applyPrivacyFilter = true; // Aplicar siempre por política de privacidad
      const processedBase64 = await ImageProcessor.compressForUpload(imageUri, applyPrivacyFilter);

      const _processingTime = Date.now() - startTime;
      const _sizeKB = (processedBase64.length * 3) / 4 / 1024;
      // Image processed successfully

      // Step 3: Validate processed image
      onProgress?.(50);
      if (!processedBase64 || processedBase64.length === 0) {
        throw new Error('El procesamiento de imagen falló - resultado vacío');
      }

      // Step 4: Upload to storage via simple Edge Function
      onProgress?.(70);
      // Uploading processed image
      // Upload parameters configured

      if (this.USE_PRIVATE_BUCKETS) {
        // New flow: Private bucket with signed URL
        const uploadResult = await this.uploadToPrivateBucket(
          processedBase64,
          salonId,
          clientId,
          photoType
        );
        // Image uploaded to private bucket

        onProgress?.(100);
        // Private upload process completed successfully
        // Upload completed
        return uploadResult;
      } else {
        // Legacy flow: Public bucket
        const publicUrl = await this.uploadProcessedImage(
          processedBase64,
          salonId,
          clientId,
          photoType
        );
        // Image uploaded to public bucket

        onProgress?.(100);
        // Public upload process completed successfully
        // Upload completed
        return { success: true, publicUrl };
      }
    } catch (error: unknown) {
      // Secure upload failed
      logger.error('Secure upload failed', 'SecureImageUpload', error);
      return {
        success: false,
        error: (error as any)?.message || 'Failed to upload image',
      };
    }
  }

  /**
   * Get current salon ID from auth store
   */
  private static async getCurrentSalonId(): Promise<string | null> {
    const { user } = useAuthStore.getState();
    return user?.salonId || null;
  }

  /**
   * Upload processed image to private bucket with signed URL
   */
  private static async uploadToPrivateBucket(
    imageBase64: string,
    salonId: string,
    clientId: string,
    photoType: 'before' | 'after' | 'desired'
  ): Promise<SecureUploadResult> {
    let lastError: Error | unknown;

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        const payload = {
          imageBase64,
          salonId,
          clientId,
          photoType,
          usePrivateBucket: true, // Flag to tell Edge Function to use private bucket
        };

        const { data, error } = await supabase.functions.invoke('upload-photo', {
          body: payload,
        });

        if (error) throw error;
        if (!data || !data.success)
          throw new Error(data?.error || 'Edge Function reportó un fallo');
        if (!data.signedUrl && !data.publicUrl) throw new Error('Edge Function no devolvió URL');

        return {
          success: true,
          signedUrl: data.signedUrl,
          publicUrl: data.signedUrl, // For backward compatibility
          bucket: data.bucket,
          path: data.path,
          expiresAt: data.expiresAt,
        };
      } catch (error: unknown) {
        lastError = error;
        logger.warn(`Private bucket upload attempt ${attempt} failed`, 'SecureImageUpload', error);
        if (attempt < this.MAX_RETRIES) {
          const delay = this.RETRY_DELAY * attempt;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw new Error(
      (lastError as any)?.message ||
        'Failed to upload image to private bucket after multiple attempts'
    );
  }

  /**
   * Upload processed image via simple Edge Function (Legacy)
   */
  private static async uploadProcessedImage(
    imageBase64: string,
    salonId: string,
    clientId: string,
    photoType: 'before' | 'after' | 'desired'
  ): Promise<string> {
    let lastError: Error | unknown;

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        const payload = { imageBase64, salonId, clientId, photoType };
        const { data, error } = await supabase.functions.invoke('upload-photo', { body: payload });

        if (error) throw error;
        if (!data) throw new Error('Edge Function no devolvió data');
        if (!data.success) throw new Error(data.error || 'Edge Function reportó un fallo');
        if (!data.publicUrl) throw new Error('Edge Function no devolvió publicUrl');

        return data.publicUrl;
      } catch (error: unknown) {
        lastError = error;
        logger.warn(`Upload attempt ${attempt} failed`, 'SecureImageUpload', error);
        if (attempt < this.MAX_RETRIES) {
          const delay = this.RETRY_DELAY * attempt;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw new Error(
      (lastError as any)?.message || 'Failed to upload image after multiple attempts'
    );
  }

  /**
   * Refresh a signed URL if it's expired or close to expiring
   */
  static async refreshSignedUrl(
    bucket: string,
    path: string,
    _currentUrl?: string
  ): Promise<string | null> {
    try {
      const result = await getSignedUrl(bucket, path, { forceRefresh: true });
      return result.url;
    } catch (error: unknown) {
      logger.error('Failed to refresh signed URL', 'SecureImageUpload', error);
      return null;
    }
  }

  /**
   * Get a fresh signed URL for an existing file
   */
  static async getSignedUrlForFile(
    bucket: string,
    path: string,
    expiresIn = 3600
  ): Promise<string | null> {
    try {
      const result = await getSignedUrl(bucket, path, { expiresIn });
      return result.url;
    } catch (error: unknown) {
      logger.error('Failed to get signed URL', 'SecureImageUpload', error);
      return null;
    }
  }

  /**
   * Delete an anonymized image (supports private signed and public legacy URLs)
   */
  static async deleteAnonymizedImage(urlOrSigned: string): Promise<boolean> {
    try {
      const url = new URL(urlOrSigned);
      let bucket = '';
      let filePath = '';

      // Private signed URL: /storage/v1/object/sign/<bucket>/<path>?token=...
      const signedMatch = url.pathname.match(/\/storage\/v1\/object\/sign\/([^/]+)\/(.+)/);
      if (signedMatch) {
        bucket = signedMatch[1];
        filePath = signedMatch[2];
      } else {
        // Legacy public URL: /storage/v1/object/public/<bucket>/<path>
        const publicMatch = url.pathname.match(/\/storage\/v1\/object\/public\/([^/]+)\/(.+)/);
        if (publicMatch) {
          bucket = publicMatch[1];
          filePath = publicMatch[2];
        }
      }

      if (!bucket || !filePath) {
        throw new Error('Invalid storage URL format');
      }

      const { error } = await supabase.storage.from(bucket).remove([filePath]);
      if (error) {
        logger.error('Failed to delete anonymized image', 'SecureImageUpload', error);
        return false;
      }

      logger.info('Anonymized image deleted', 'SecureImageUpload', { bucket, filePath });
      return true;
    } catch (error) {
      logger.error('Delete anonymized image failed', 'SecureImageUpload', error);
      return false;
    }
  }
}

// Export convenience functions
export const uploadAndAnonymizeImage = SecureImageUpload.uploadAndAnonymize.bind(SecureImageUpload);
export const deleteAnonymizedImage =
  SecureImageUpload.deleteAnonymizedImage.bind(SecureImageUpload);
export const refreshSignedUrl = SecureImageUpload.refreshSignedUrl.bind(SecureImageUpload);
export const getSignedUrlForFile = SecureImageUpload.getSignedUrlForFile.bind(SecureImageUpload);
