/**
 * Test Suite for Brand Service - Database Access Layer
 *
 * Tests the complete brand service implementation including:
 * - Database access and transformation
 * - Smart caching mechanisms
 * - Offline-first behavior
 * - Backward compatibility
 * - Performance requirements
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { brandService, getBrandsAsync, getBrandByIdAsync, type Brand } from '../brandService';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage');
jest.mock('@/lib/supabase');
jest.mock('@/utils/logger');

// Mock the static JSON fallback
jest.mock('../../data/brands.json', () => ({
  brands: [],
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

// Test data
const mockBrandsFromDB = [
  {
    id: 'test-brand-1',
    name: 'Test Brand 1',
    country: 'Spain',
    description: 'Test brand description',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    founded_year: null,
    logo_url: null,
    notes: null,
    parent_company: null,
    website_url: null,
  },
];

const mockProductLinesFromDB = [
  {
    id: 'test-line-1',
    brand_id: 'test-brand-1',
    name: 'Test Color Line',
    description: 'Test color line description',
    category: 'tinte' as any,
    is_active: true,
    discontinued: false,
    professional_only: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    launch_year: null,
    notes: null,
  },
  {
    id: 'test-line-2',
    brand_id: 'test-brand-1',
    name: 'Test Treatment Line',
    description: 'Test treatment line description',
    category: 'tratamiento' as any,
    is_active: true,
    discontinued: false,
    professional_only: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    launch_year: null,
    notes: null,
  },
];

const expectedTransformedBrand: Brand = {
  id: 'test-brand-1',
  name: 'Test Brand 1',
  country: 'Spain',
  description: 'Test brand description',
  lines: [
    {
      id: 'test-line-1',
      name: 'Test Color Line',
      description: 'Test color line description',
      category: 'hair-color',
      isColorLine: true,
    },
    {
      id: 'test-line-2',
      name: 'Test Treatment Line',
      description: 'Test treatment line description',
      category: 'treatment',
      isColorLine: false,
    },
  ],
};

// Mock Supabase
const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        order: jest.fn(() => ({
          data: null,
          error: null,
        })),
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            data: null,
            error: null,
          })),
        })),
      })),
      order: jest.fn(() => ({
        data: null,
        error: null,
      })),
    })),
  })),
};

jest.mock('@/lib/supabase', () => ({
  supabase: mockSupabase,
}));

describe('BrandService', () => {
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Reset AsyncStorage mock
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    mockAsyncStorage.removeItem.mockResolvedValue();
  });

  afterEach(async () => {
    // Clear cache after each test
    await brandService.invalidateCache();
  });

  describe('Database Integration', () => {
    it('should fetch brands from database successfully', async () => {
      // Mock successful database responses
      const mockBrandsQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: mockBrandsFromDB,
              error: null,
            })),
          })),
        })),
      };

      const mockLinesQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            eq: jest.fn(() => ({
              order: jest.fn(() => ({
                data: mockProductLinesFromDB,
                error: null,
              })),
            })),
          })),
        })),
      };

      mockSupabase.from.mockReturnValueOnce(mockBrandsQuery).mockReturnValueOnce(mockLinesQuery);

      const brands = await getBrandsAsync();

      expect(brands).toHaveLength(1);
      expect(brands[0]).toEqual(expectedTransformedBrand);
    });

    it('should handle database errors gracefully', async () => {
      // Mock database error
      const mockQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: null,
              error: { message: 'Database connection failed' },
            })),
          })),
        })),
      };

      mockSupabase.from.mockReturnValue(mockQuery);

      // Should fall back to static data
      await expect(getBrandsAsync()).rejects.toThrow();
    });
  });

  describe('Category Mapping', () => {
    it('should map database categories to interface categories correctly', async () => {
      const testCases = [
        { dbCategory: 'tinte', expected: 'hair-color', isFormulable: true },
        { dbCategory: 'decolorante', expected: 'bleaching', isFormulable: true },
        { dbCategory: 'tratamiento', expected: 'treatment', isFormulable: false },
        { dbCategory: 'oxidante', expected: 'developer', isFormulable: false },
        { dbCategory: 'otro', expected: 'other', isFormulable: false },
      ];

      for (const testCase of testCases) {
        const mockLine = {
          ...mockProductLinesFromDB[0],
          category: testCase.dbCategory as any,
        };

        const mockBrandsQuery = {
          select: jest.fn(() => ({
            eq: jest.fn(() => ({
              order: jest.fn(() => ({
                data: mockBrandsFromDB,
                error: null,
              })),
            })),
          })),
        };

        const mockLinesQuery = {
          select: jest.fn(() => ({
            eq: jest.fn(() => ({
              eq: jest.fn(() => ({
                order: jest.fn(() => ({
                  data: [mockLine],
                  error: null,
                })),
              })),
            })),
          })),
        };

        mockSupabase.from.mockReturnValueOnce(mockBrandsQuery).mockReturnValueOnce(mockLinesQuery);

        const brands = await getBrandsAsync();
        const line = brands[0].lines[0];

        expect(line.category).toBe(testCase.expected);
        expect(line.isColorLine).toBe(testCase.isFormulable);
      }
    });
  });

  describe('Caching Mechanisms', () => {
    it('should cache data in AsyncStorage after successful fetch', async () => {
      // Mock successful database response
      const mockBrandsQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: mockBrandsFromDB,
              error: null,
            })),
          })),
        })),
      };

      const mockLinesQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            eq: jest.fn(() => ({
              order: jest.fn(() => ({
                data: mockProductLinesFromDB,
                error: null,
              })),
            })),
          })),
        })),
      };

      mockSupabase.from.mockReturnValueOnce(mockBrandsQuery).mockReturnValueOnce(mockLinesQuery);

      await getBrandsAsync();

      // Verify cache was written
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'brand_service_cache_brands',
        JSON.stringify([expectedTransformedBrand])
      );
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'brand_service_cache_timestamp',
        expect.any(String)
      );
    });

    it('should load from cache when available and not expired', async () => {
      // Mock cache hit
      const cacheTimestamp = Date.now() - 60000; // 1 minute ago (not expired)
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(JSON.stringify([expectedTransformedBrand]))
        .mockResolvedValueOnce(cacheTimestamp.toString());

      const brands = await getBrandsAsync();

      expect(brands).toEqual([expectedTransformedBrand]);
      // Should not call database if cache is valid
      expect(mockSupabase.from).not.toHaveBeenCalled();
    });

    it('should refresh cache when expired', async () => {
      // Mock expired cache
      const expiredTimestamp = Date.now() - 10 * 60 * 1000; // 10 minutes ago (expired)
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(JSON.stringify([expectedTransformedBrand]))
        .mockResolvedValueOnce(expiredTimestamp.toString());

      // Mock fresh database response
      const mockBrandsQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: mockBrandsFromDB,
              error: null,
            })),
          })),
        })),
      };

      const mockLinesQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            eq: jest.fn(() => ({
              order: jest.fn(() => ({
                data: mockProductLinesFromDB,
                error: null,
              })),
            })),
          })),
        })),
      };

      mockSupabase.from.mockReturnValueOnce(mockBrandsQuery).mockReturnValueOnce(mockLinesQuery);

      const brands = await getBrandsAsync();

      expect(brands).toEqual([expectedTransformedBrand]);
      // Should call database to refresh expired cache
      expect(mockSupabase.from).toHaveBeenCalled();
    });
  });

  describe('Performance Requirements', () => {
    it('should complete cache hit in under 5ms', async () => {
      // Mock cache hit
      const cacheTimestamp = Date.now() - 60000; // 1 minute ago
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(JSON.stringify([expectedTransformedBrand]))
        .mockResolvedValueOnce(cacheTimestamp.toString());

      const startTime = Date.now();
      await getBrandsAsync();
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(5);
    });

    it('should avoid duplicate requests when called multiple times', async () => {
      // Mock database response
      const mockBrandsQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: mockBrandsFromDB,
              error: null,
            })),
          })),
        })),
      };

      const mockLinesQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            eq: jest.fn(() => ({
              order: jest.fn(() => ({
                data: mockProductLinesFromDB,
                error: null,
              })),
            })),
          })),
        })),
      };

      mockSupabase.from.mockReturnValueOnce(mockBrandsQuery).mockReturnValueOnce(mockLinesQuery);

      // Call multiple times simultaneously
      const promises = [getBrandsAsync(), getBrandsAsync(), getBrandsAsync()];

      const results = await Promise.all(promises);

      // All should return the same data
      results.forEach(brands => {
        expect(brands).toEqual([expectedTransformedBrand]);
      });

      // Database should only be called once
      expect(mockSupabase.from).toHaveBeenCalledTimes(2); // Once for brands, once for lines
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain exact interface compatibility', async () => {
      // Mock successful response
      const mockBrandsQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: mockBrandsFromDB,
              error: null,
            })),
          })),
        })),
      };

      const mockLinesQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            eq: jest.fn(() => ({
              order: jest.fn(() => ({
                data: mockProductLinesFromDB,
                error: null,
              })),
            })),
          })),
        })),
      };

      mockSupabase.from.mockReturnValueOnce(mockBrandsQuery).mockReturnValueOnce(mockLinesQuery);

      const brand = await getBrandByIdAsync('test-brand-1');

      expect(brand).toBeDefined();
      expect(brand).toHaveProperty('id');
      expect(brand).toHaveProperty('name');
      expect(brand).toHaveProperty('country');
      expect(brand).toHaveProperty('lines');
      expect(Array.isArray(brand!.lines)).toBe(true);

      const line = brand!.lines[0];
      expect(line).toHaveProperty('id');
      expect(line).toHaveProperty('name');
      expect(line).toHaveProperty('category');
      expect(line).toHaveProperty('isColorLine');
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should use stale cache when database fails', async () => {
      // Mock stale cache (expired but available)
      const _staleTimestamp = Date.now() - 10 * 60 * 1000; // 10 minutes ago
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(null) // No valid cache
        .mockResolvedValueOnce(null) // No valid timestamp
        .mockResolvedValueOnce(JSON.stringify([expectedTransformedBrand])); // Stale cache available

      // Mock database failure
      const mockQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: null,
              error: { message: 'Database unavailable' },
            })),
          })),
        })),
      };

      mockSupabase.from.mockReturnValue(mockQuery);

      const brands = await getBrandsAsync();

      expect(brands).toEqual([expectedTransformedBrand]);
    });

    it('should handle malformed cache data gracefully', async () => {
      // Mock corrupted cache
      mockAsyncStorage.getItem
        .mockResolvedValueOnce('invalid json data')
        .mockResolvedValueOnce(Date.now().toString());

      // Mock successful database fallback
      const mockBrandsQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: mockBrandsFromDB,
              error: null,
            })),
          })),
        })),
      };

      const mockLinesQuery = {
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            eq: jest.fn(() => ({
              order: jest.fn(() => ({
                data: mockProductLinesFromDB,
                error: null,
              })),
            })),
          })),
        })),
      };

      mockSupabase.from.mockReturnValueOnce(mockBrandsQuery).mockReturnValueOnce(mockLinesQuery);

      const brands = await getBrandsAsync();

      expect(brands).toEqual([expectedTransformedBrand]);
    });
  });

  describe('Cache Management', () => {
    it('should provide cache status information', async () => {
      const status = await brandService.getCacheStatus();

      expect(status).toHaveProperty('hasMemoryCache');
      expect(status).toHaveProperty('hasStorageCache');
    });

    it('should invalidate cache completely', async () => {
      await brandService.invalidateCache();

      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('brand_service_cache_brands');
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('brand_service_cache_timestamp');
    });
  });
});
