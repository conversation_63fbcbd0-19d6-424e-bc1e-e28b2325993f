import type { FormulationStep, ProductMix } from '@/types/formulation';
import { getBrandPack } from '@/data/brand-packs';
import { estimateColorGrams } from '@/services/formulation/consumption-estimator';
import {
  computeDeveloperAmount,
  parseRatio,
  simplifyRatioFromAmounts,
  updateProductNameRatio,
} from '@/services/formulation/mixing-engine';

export interface HairContext {
  hairLengthCm?: number;
  hairDensity?: string; // 'Baja' | 'Media' | 'Alta'
  hairThickness?: string; // 'Fino' | 'Medio' | 'Grueso'
}

type Area = 'roots' | 'full' | 'mids_ends' | 'toner_global' | 'bleach';

function classifyArea(step: FormulationStep): Area | null {
  const t = `${step.stepTitle} ${step.instructions}`.toLowerCase();
  if (/(ra[ií]z|raíces|retoque|root)/.test(t)) return 'roots';
  if (/(global|todo el cabello|aplicaci[oó]n global|full color)/.test(t)) return 'full';
  if (/(medios|mids|puntas|ends)/.test(t)) return 'mids_ends';
  // Spanish synonyms for toning/deposit
  if (/(tonaliz|tono|tonificar|matiz|matizar|depositar|gloss|glaze|toner)/.test(t))
    return 'toner_global';
  if (/(decolor|bleach|polvo)/.test(t)) return 'bleach';
  return null;
}

function detectRatioFromMix(mix: ProductMix[], fallback: string): string {
  // Look for "(mezcla X:Y)" in any product name
  for (const m of mix) {
    const nm = m.productName || '';
    const match = nm.match(/\(\s*(?:mezcla|mix)\s*(\d+(?:\.\d+)?\s*:\s*\d+(?:\.\d+)?)\s*\)/i);
    if (match) return match[1].replace(/\s/g, '');
  }
  return fallback;
}

function isDeveloper(name: string): boolean {
  return /oxidante|developer|per[óo]xido|revelador|oxigenada|welloxon|oxydant/i.test(name);
}

function scaleColorItemsTo(total: number, items: ProductMix[]): ProductMix[] {
  if (items.length === 0) return items;
  const current = items.reduce((a, b) => a + (b.quantity || 0), 0);
  const target = total;
  if (current <= 0) {
    const equal = Math.round(target / items.length / 5) * 5 || 5;
    let sum = 0;
    const out = items.map((it, idx) => {
      const q = idx === items.length - 1 ? Math.max(5, target - sum) : Math.max(5, equal);
      sum += q;
      return { ...it, quantity: q, unit: 'gr' };
    });
    return out;
  }
  // keep proportions
  let sum = 0;
  const out = items.map((it, idx) => {
    const prop = (it.quantity || 0) / current;
    let q = Math.round((prop * target) / 5) * 5;
    if (q < 5) q = 5;
    if (idx === items.length - 1) q = Math.max(5, target - sum);
    sum += q;
    return { ...it, quantity: q, unit: 'gr' };
  });
  return out;
}

export function normalizeFormulaSteps(
  steps: FormulationStep[] | undefined,
  brand?: string,
  line?: string,
  hair?: HairContext
): FormulationStep[] | undefined {
  if (!steps || steps.length === 0) return steps;
  const pack = getBrandPack(brand, line);

  return steps.map(step => {
    if (!step.mix || step.mix.length === 0) return step;
    const area = classifyArea(step);
    if (!area) return step;

    const colorItems = step.mix.filter(m => m.unit === 'gr' && !isDeveloper(m.productName));
    const devIndex = step.mix.findIndex(m => isDeveloper(m.productName));

    // Estimate grams for this area
    const grams = estimateColorGrams({
      application: area === 'bleach' ? 'mids_ends' : area,
      hairLengthCm: hair?.hairLengthCm,
      density: hair?.hairDensity,
      thickness: hair?.hairThickness,
    });

    // Determine ratio
    const fallbackRatio =
      area === 'toner_global' && pack?.defaultMixRatio
        ? pack.defaultMixRatio
        : pack?.defaultMixRatio || '1:1';
    const ratio = detectRatioFromMix(step.mix, fallbackRatio);

    // Scale color items
    const scaledColors = scaleColorItemsTo(grams, colorItems);

    // Compute developer amount
    const developerMl =
      area === 'bleach'
        ? Math.max(10, Math.round((grams * 2) / 5) * 5)
        : computeDeveloperAmount(grams, ratio);
    const label = simplifyRatioFromAmounts(grams, developerMl);

    // Rebuild mix
    const others = step.mix.filter(
      m =>
        !(colorItems as ProductMix[]).includes(m) &&
        (devIndex < 0 || step.mix.indexOf(m) !== devIndex)
    );
    const outMix: ProductMix[] = [];

    // Update names with ratio label
    scaledColors.forEach((c, idx) => {
      outMix.push({ ...c, productName: updateProductNameRatio(c.productName, label) });
    });

    if (devIndex >= 0) {
      const dev = step.mix[devIndex];
      outMix.push({
        ...dev,
        quantity: developerMl,
        unit: 'ml',
        productName: updateProductNameRatio(dev.productName, label),
      });
    } else {
      // Add developer if missing (use default developer volume in the name when possible)
      const baseName = `${brand || 'Marca'} Oxidante ${pack?.defaultDeveloper || 10} vol`;
      outMix.push({
        productId: `dev-${Date.now()}`,
        productName: updateProductNameRatio(baseName, label),
        quantity: developerMl,
        unit: 'ml',
      });
    }

    others.forEach(o => outMix.push(o));

    return { ...step, mix: outMix };
  });
}
